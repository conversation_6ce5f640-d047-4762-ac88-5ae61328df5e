{"spider": "./jar/pg.jar;md5;04d74f2c921acbc9a2e20bd880a6e630", "lives": [{"name": "初秋语•综合", "type": 0, "url": "./list.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "Ray•综合", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/dxawi/0/main/tvlive.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "俊于•综合", "type": 0, "url": "http://home.jundie.top:81/Cat/tv/live.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "YuanHsing•油管", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/YuanHsing/YouTube_to_m3u/main/youtube.m3u", "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}], "wallpaper": "https://jianbian.chuqiuyu.workers.dev", "sites": [{"key": "豆瓣", "name": "豆瓣", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 0, "changeable": 1, "ext": "./json/douban.json"}, {"key": "drpy_js_豆瓣", "name": "搜索", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/drpy.js", "searchable": 1, "quickSearch": 0, "changeable": 1}, {"key": "drpy_js_磁力熊搜索", "name": "熊搜", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/cilixiong.js", "searchable": 0, "quickSearch": 0, "changeable": 1}, {"key": "csp_YGP", "name": "预告片", "type": 3, "api": "csp_YGP", "searchable": 1, "quickSearch": 1, "changeable": 0}, {"key": "csp_<PERSON><PERSON>", "name": "哔哩", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "style": {"type": "rect", "ratio": 1.755}, "filterable": 1, "changeable": 0, "ext": {"json": "./json/bili.json", "cookie": ""}}, {"key": "bili_open", "name": "哔哩貓", "type": 3, "api": "./cat/js/bili_open.js", "searchable": 1, "changeable": 0, "ext": {"categories": "沙雕动画#帕梅拉#音乐#舞蹈#风景#美食#科普#历史#法考#医考#法医学", "cookie": ""}}, {"key": "FirstAid", "name": "急救 ", "type": 3, "api": "csp_FirstAid", "searchable": 0, "quickSearch": 0, "changeable": 0, "style": {"type": "rect", "ratio": 3.8}}, {"key": "JustLive", "name": "JustLive", "type": 3, "api": "csp_JustLive", "searchable": 1, "changeable": 0}, {"key": "lf_js_lf_live", "name": "直播", "type": 3, "api": "./lib/lf_live_min.js", "style": {"type": "oval"}, "searchable": 1, "changeable": 0, "quickSearch": 1, "filterable": 1, "ext": "./js/lf_live.txt"}, {"key": "drpy_js_ikanbot3", "name": "爱看", "type": 3, "api": "./lib/drpy2.min.js", "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "./js/ikanbot3.js"}, {"key": "T4-duanju5", "name": "短剧屋", "type": 4, "api": "https://catbox.n13.club/duanju5.php", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1, "ext": true}, {"key": "看球", "name": "看球", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 1, "changeable": 0, "style": {"type": "list"}}, {"key": "88js", "name": "88看球", "type": 3, "api": "./lib/drpy.min.js", "ext": "./js/88看球.js", "style": {"type": "list"}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "drpy_js_310直播", "name": "310直播", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "style": {"type": "list"}, "ext": "./js/310直播.js"}, {"key": "csp_XPath_企鹅体育", "name": "企鹅", "type": 3, "api": "csp_XPath", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./json/企鹅直播.json"}, {"key": "酷狗", "name": "酷狗", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "changeable": 0}, {"key": "Iktv", "name": "KTV", "type": 3, "api": "csp_Iktv", "searchable": 1, "changeable": 0}, {"key": "Yin<PERSON><PERSON><PERSON>", "name": "音悦台", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "changeable": 0}, {"key": "PushShare", "name": "资源分享", "type": 3, "api": "csp_PushShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$./json/pushshare.txt$$$db$$$1", "style": {"type": "list"}}, {"key": "PikPakShare", "name": "PikPak分享", "type": 3, "api": "csp_PikPakShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$./json/pikpakclass.json$$$./json/pikpakclass.json.db.gz"}, {"key": "AliShare", "name": "影视分享", "type": 3, "api": "csp_AliShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$./json/alishare.txt$$$db$$$1"}, {"key": "AliShareEBook", "name": "书籍分享", "type": 3, "api": "csp_AliShare", "searchable": 1, "quickSearch": 0, "changeable": 0, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$./json/alishare.ebook.txt$$$db$$$1", "style": {"type": "list"}}, {"key": "ThunderShare", "name": "迅雷分享", "type": 3, "api": "csp_ThunderShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$./json/thundershare.txt"}, {"key": "QuarkShare", "name": "夸克分享", "type": 3, "api": "csp_QuarkShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$./json/quarkshare.txt"}, {"key": "SambaShare", "name": "Samba分享", "type": 3, "api": "csp_SambaShare", "searchable": 0, "quickSearch": 0, "changeable": 0, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$./json/sambashare.txt"}, {"key": "配置中心", "name": "配置中心", "type": 3, "api": "csp_Config", "searchable": 0, "changeable": 0, "style": {"type": "rect", "ratio": 1}, "ext": "http://127.0.0.1:9978/file/TV/token.json"}, {"key": "<PERSON><PERSON>", "name": "HDmoli", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.hdmoli.pro/$$$null$$$1$$$./json/moli.json"}, {"key": "csp_WoGG", "name": "哥哥", "type": 3, "api": "csp_Wogg", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.wogg.xyz/$$$1$$$./json/wogg.json", "timeout": 30}, {"key": "影视车", "name": "影视车", "type": 3, "api": "csp_Wogg", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.weixine.link/$$$1$$$./json/wogg.json", "timeout": 30}, {"key": "csp_Wobg", "name": "表哥", "type": 3, "api": "csp_Wobg", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://wobge.run.goorm.io$$$1$$$./json/wogg.json", "timeout": 30}, {"key": "csp_Wobg土豆网盘弹幕", "name": "土豆", "type": 3, "api": "csp_Wobg", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://ali.lvdoui.top$$$1$$$./json/wogg.json", "timeout": 30}, {"key": "<PERSON><PERSON><PERSON>", "name": "影視車", "type": 3, "api": "csp_<PERSON>scapp", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "playerType": 1, "ext": "1"}, {"key": "Bt0", "name": "不太灵", "type": 3, "api": "csp_Bt0", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "null$$$null$$$1"}, {"key": "美剧迷", "name": "美剧迷", "type": 3, "api": "csp_<PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.meijumi.xyz/$$$null$$$1"}, {"key": "Ppxzy", "name": "皮皮虾", "type": 3, "api": "csp_Ppxzy", "quickSearch": 1, "changeable": 1, "filterable": 1, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://ppxzy.net/$$$null$$$1", "timeout": 30}, {"key": "Pan<PERSON>", "name": "盘Ta", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.91panta.cn/$$$null$$$1"}, {"key": "csp_Hdhive", "name": "影巢", "type": 3, "api": "csp_Hdhive", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "新6V", "name": "新6V", "type": 3, "api": "csp_Xb6v", "searchable": 1, "changeable": 1, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.66ss.org$$$null$$$1"}, {"key": "4KHDR", "name": "4KHDR", "type": 3, "api": "csp_FourKHDR", "ext": "http://127.0.0.1:9978/file/TV/token.json$$$http://127.0.0.1:9978/file/TV/4khdr.txt", "quickSearch": 1, "changeable": 1, "timeout": 60}, {"key": "校长影视", "name": "校长", "type": 3, "api": "csp_<PERSON>", "ext": "http://127.0.0.1:9978/file/TV/token.json", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "团长资源", "name": "团长", "type": 3, "api": "csp_TZFile", "ext": "http://127.0.0.1:9978/file/TV/token.json", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "荐片弹幕版", "name": "荐片", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./json/jianpian.json$$$1", "timeout": 60}, {"key": "河马", "name": "河马", "type": 3, "api": "csp_<PERSON><PERSON>s", "playerType": 1, "ext": "https://m.jmzp.net.cn$$$null$$$1"}, {"key": "天天", "name": "天天", "type": 3, "api": "csp_TTian", "playerType": 1, "ext": "http://op.ysdqjs.cn$$$null$$$1"}, {"key": "追剧", "name": "追剧", "type": 3, "api": "csp_TTian", "playerType": 1, "ext": "http://app.kzjtv.com$$$null$$$1"}, {"key": "B<PERSON><PERSON>_spider", "name": "哔滴", "api": "csp_Bdys01", "type": 3, "filterable": 1, "quickSearch": 1, "searchable": 1, "ext": "https://www.bdys03.com/$$$null$$$1", "timeout": 30}, {"key": "<PERSON><PERSON><PERSON>", "name": "南瓜", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "changeable": 1, "ext": "null$$$1"}, {"key": "蝴蝶", "name": "蝴蝶", "type": 3, "api": "csp_<PERSON><PERSON>", "playerType": 2, "ext": "https://yundun-hdsign.zhuifeng100.cn|kT2taP|75f7fac7f56d812d1f9ce89d9f312984|b7688bd6347da0b2b62515b0730b872b$$$null$$$1"}, {"key": "tvbsk", "name": "TVB", "type": 3, "api": "csp_<PERSON><PERSON>", "playerType": 2, "ext": "CWL+IfA5s3TICYJ8lk+lYcjPA/WXNYE4LRTfUSa9I+X9ibZY05syLe/O7u2miaiYRiSaKE51xQOxm38YeL27jRUyF6+GJKVQArnP7QbC+qw2okju8YACfPzD3rpQo8uckl2rQh925A6lp4VD0ELHJrUvDGTlojwKtynX78d5E88HbdSsCV5pIueNPH5+z42FBt21KGN5lnE7/qqEGpSyFzbARD4aWuhSHsun7GQU9yaXZn0F4l4o2YYaEvRtsz4z9Mo8+7aj2nZsCkFWLNpgv6kzFNohSTWVDkajg5aj6s7d8CPNIvvC+gr0zm79fuuvNQF6NfaiBOrvTwamMJa3F3w==$$$null$$$1"}, {"key": "攻阁", "name": "攻阁", "type": 3, "api": "csp_Appto", "playerType": 2, "ext": "cepveeMdL3qzBp3D86fBcgucTPCRb88AXoITrxvmypEbJbvnC/4ipv8+gLYBHlIYBFWfcZr3fkzfxQ8gAmZel3ej+I4REk7LnHUBNtZ9P0S97uBwswzPtz3voKW2TcVbBKQNmA2XowTEbiwPTJTkP8H3KpHtipqyoPNDOoDXGviUlUQqn3+A8FSXDyoySvEvG3eTw64Pj48USmhQvQPG8Jvhpo9+YRSg3pa6vv9hUjYmgheP1X8rA6zqy9N4L++/NL5gszaaf8yH4omdSxtYrj330jkXC4vPWU5fE4p6vRrq7mxA5Bp48Lhp3GjrKDyoOjBrI7+yvKarHhyoklswLFD3vfmM4J3osXIoUnwpXBBktdkDgFzUHvxY8BUeCTMBIPQfGiS8v88z6OPR8ven+KdreBl8BV6RqFK7vHb/OM9O5qLNpsPNSImbet1tdaAbSyEUxNUjplKpbGCxM5RZgcdyl/prki2aDcpICE54hcagK8d0JvmohzpLE2xper9AdIP1lD+sYndodhIYejNPFP3BiKPdRkBTrcQbicyp9oA6OIg2/aoJRvH1tai/OcozC5DaT0TPg1eo1NhodMKHa/CZ3Srg0ij9ocYwbX/e42JrBCgnp0gCph/oz7EipyUDI3n437r/skJ/cDEPColQ8PfT2LQE7hPGbp1/mDG3aKLNSuVCiMkQy66a6jyfyPKd8GqzpkGPCrPpneBNsMjGqLCveMl/PgXloTG7BZbg+X0VoXpCjOC3u9n2/Z4xqdnc+Dhw1LBXpdhvLKKl6d9K6/dOVgWRIfYx2I5DPQn79fU47+UkPxPA7qAaU0v9tRXYKXfRh2yJcijXre4/JzBxSmR2owrKVmnXcwBlsIseor36jbISj4TJdA+gVP5Ayfh5a09kpFDYvuPTgRhs4O2z2t3LBnQzLE3KBT2xPznJurYInw7P4yVxDOhpFkGGPZNV/+ehl9CcWwhZzUvHqtPD2zqZXKZvgB3BxR4b1NTgtRY+h28BFD4N9ejY5aJ9yS50kXYTIhXvwrzbRAjHLf+sj4t0gybV7S4osR92WoOyf3rpyeUQdO+9Bqfl5fWvqVl9tMC7/ukkgqlb2p+R1TLbiqD+ruQNMYGdETK4Nl4dkgUVOxOmabWH6RjKiykA5GQWKwo2PLrxE4fwhgkNCYlVDP8gYUBPdo5SeXM7p2ZKrRs7hlFGA2WbSnC4Ni05mwCmlOw9pJzbCXUMalJcD+9b53sLKgb4JYWjEmPe1/4fpH+xwZPlNDjQXGVolNxGkUodNzUFTECEWZLjGtqwQiQYNrErYXRYfea5Vx02WW1JyrHbYJ4FJv61h1KQ6fXAxty+OdtMTU95TwpNr8E62aFNSMQ==$$$null$$$1"}, {"key": "视觉", "name": "视觉", "type": 3, "api": "csp_Appto", "searchable": 1, "quickSearch": 1, "playerType": 2, "filterable": 1, "ext": "jrytgWz8W3BsIGIs/QktiAoZ7DtDplwV9Pg7bF8WHnTF5F2klyA77RixFCdoINLj8nCqhlMNuwB1Pb2inNFkZjh8RyGN7ZiOnqpb+aNKTWc/Jck9cfN+SNPCNjjnciGaRqH02Q0iKQBwU/zWOS96pWBxD9lY0qzeNbz4IrokjL7REfCrRVCcOVrXRuY+Pqa4W64no+kOxEzgiXo2D2z1M5rewxwQD0hMFq8psl+TndXgy3T02vKjjNzGT6U7IqR2O1BPEj7GbFWtGZHeYsMp9/hvlhKj6zpekdc/TuxTAQ5+zLGSoL5rrwLOlEr+OSpeT8C6bDxI9wvvmIoFMLMmgHEo2weV2KgP6r6MONUo7B7UlSWSydzHsXYKjcM0Z0kk5GO6eCn2/H3nDnhccyOtL101S4JRTPAsF4FStqYRibK/I1jwUd8+tUxUKtHRqb2tSD5g0HzcKjZGSCvw/drZKG2dEtOEFhb8f6b3f+UH6f8oGOPrw5n/sUmWoMtNV6qO0uXgisoIBGcesdlXaZd7ZmFwecyipGGkUh2L+uHKTpGBYXpwYZweuESOdeaemVcEiPWs0uvWxi3fQHvhYyVOORJxrVWwwkxAOUGbQCCXxeghANz9NPwK6ff6siV0+p2486wXV55TX8rR8UakzQ1f7/5PaFp5FnjQEzwmkPF/zzE4=$$$null$$$1"}, {"key": "Ying<PERSON><PERSON>", "name": "影搜", "type": 3, "api": "csp_Ying<PERSON>o", "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$1"}, {"key": "盘友圈", "name": "盘友圈", "type": 3, "api": "csp_Panyq", "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json"}, {"key": "88Pan", "name": "88网盘", "type": 3, "api": "csp_EightEight", "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://662688.xyz"}, {"key": "PikaSo", "name": "皮卡搜", "type": 3, "api": "csp_PikaSo", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.pikaso.top/$$$null"}, {"key": "csp_HunHePan", "name": "混合盘", "type": 3, "api": "csp_HunHePan", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$null$$$1"}, {"key": "秒搜", "name": "秒搜", "type": 3, "api": "csp_Miao<PERSON>ou", "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json"}, {"key": "<PERSON><PERSON><PERSON>", "name": "LIBVIO", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://www.libvio.fun$$$null"}, {"key": "DaPanSo", "name": "大盘搜", "type": 3, "api": "csp_DaPanSo", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://dapanso.com$$$null"}, {"key": "<PERSON><PERSON><PERSON>", "name": "千帆", "type": 3, "api": "csp_<PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$https://pan.qianfan.app$$$null$$$QianFanID="}, {"key": "YunSo", "name": "小云", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json"}, {"key": "YunPanOne", "name": "云盘", "type": 3, "api": "csp_YunPanOne", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json"}, {"key": "Gitcafe", "name": "纸条", "type": 3, "api": "csp_Gitcafe", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "小紙條", "name": "小紙條", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "七夜", "name": "七夜", "type": 3, "api": "csp_Dovx", "searchable": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "盤搜索", "name": "盤搜索", "type": 3, "api": "csp_PanSearch", "searchable": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "找資源", "name": "找資源", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json$$$fanty$$$qqq111", "timeout": 30}, {"key": "易搜", "name": "易搜", "type": 3, "api": "csp_<PERSON>o", "ext": "http://127.0.0.1:9978/file/TV/token.json$$$satoken=1aee2366-2ff4-4436-b321-a14db23e8294", "timeout": 30}, {"key": "盘搜", "name": "盘搜", "type": 3, "api": "csp_Pan<PERSON>ou", "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "UP云搜", "name": "云搜", "type": 3, "api": "csp_UpYun", "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "Funlet<PERSON>", "name": "趣盘搜", "type": 3, "api": "csp_<PERSON>letu", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json"}, {"key": "QuarkPanso", "name": "夸克盘搜", "type": 3, "api": "csp_QuarkPanso", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 30, "ext": "http://127.0.0.1:9978/file/TV/token.json"}, {"key": "<PERSON><PERSON><PERSON>", "name": "蚂蚁", "type": 3, "api": "csp_<PERSON><PERSON>ys", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1}, {"key": "Maolv", "name": "毛驴", "type": 3, "api": "csp_Maolv", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1}, {"key": "csp_<PERSON>", "name": "快看", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "name": "一起看", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1}, {"key": "泥巴", "name": "泥巴", "type": 3, "api": "csp_Ni<PERSON>i", "searchable": 1, "changeable": 1, "ext": "0$$$"}, {"key": "星星", "name": "星星", "type": 3, "api": "csp_Star", "searchable": 1, "changeable": 1}, {"key": "影视", "name": "影视", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "changeable": 1}, {"key": "櫻花", "name": "櫻花", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "changeable": 1}, {"key": "巴士", "name": "巴士", "type": 3, "api": "csp_Dm84", "searchable": 1, "changeable": 1}, {"key": "drpy_js_4KHDR", "name": "4KHDR[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/4khdr.js", "playerType": "1", "searchable": 1, "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_MP4US", "name": "MP4电影[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/mp4us.js", "playerType": "1", "searchable": 1, "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_电影港", "name": "电影港[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/dygang.js", "playerType": "1", "searchable": 1, "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_kuba", "name": "酷吧[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/kuba.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_xb6v", "name": "新版6v[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/xb6v.js", "playerType": "1", "searchable": 1, "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_美剧迷p", "name": "美剧迷[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/meijumi.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_ddys", "name": "低端[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/ddys.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_libvio", "name": "libvio[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/libvio.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_tzfile", "name": "团长[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/tzfile.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_dydhhy", "name": "dydhhy[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/dydhhy.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_97tvs", "name": "97tvs[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/97tvs.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_rrdyw", "name": "rrdyw[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/rrdyw.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_校长影视", "name": "校长[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/xzys.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_yyetsp", "name": "人人搜[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/yyets.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_奇妙搜[夸]", "name": "奇妙搜[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/qimiao.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_愛盤搜", "name": "爱盘搜[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/aipanso.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_趣盤搜", "name": "趣盘搜[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/funletu.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "drpy_js_meow", "name": "meow搜[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/meow.js", "quickSearch": 1, "changeable": 1, "timeout": 30}, {"key": "push_quark", "name": "夸克推送", "type": 3, "api": "csp_Quark", "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/quark.txt", "timeout": 30}, {"key": "push_agent", "name": "推送", "type": 3, "api": "csp_<PERSON>ush", "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.json", "timeout": 30}, {"key": "csp_BLSGod", "name": "80S影视", "type": 3, "api": "csp_BLSGod", "playerType": 1, "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_<PERSON>i8", "name": "迅雷吧", "type": 3, "api": "csp_<PERSON>i8", "playerType": 1, "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "MeijuTT", "name": "美剧天天", "type": 3, "api": "csp_Meiju<PERSON>", "playerType": "1", "changeable": 0}, {"key": "SeedHub", "name": "SeedHub", "type": 3, "api": "csp_SeedHub", "playerType": "1", "changeable": 0}, {"key": "獨播", "name": "獨播", "type": 3, "api": "csp_XPathMacFilter", "searchable": 1, "changeable": 1, "ext": "./json/duboku.json"}, {"key": "量子", "name": "量子", "type": 1, "api": "https://cj.lziapi.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "非凡", "name": "非凡", "type": 1, "api": "http://cj.ffzyapi.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "haiwaikan", "name": "海外看", "type": 1, "api": "https://haiwaikan.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "暴風", "name": "暴風", "type": 1, "api": "https://bfzyapi.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "索尼", "name": "索尼", "type": 1, "api": "https://suoniapi.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "快帆", "name": "快帆", "type": 1, "api": "https://api.kuaifan.tv/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "本地", "name": "本地", "type": 3, "api": "csp_Local", "searchable": 0, "changeable": 0}, {"key": "AList", "name": "网盘", "type": 3, "api": "csp_AList", "searchable": 1, "changeable": 0, "ext": "./json/alist.json"}, {"key": "WebDAV", "name": "云盘", "type": 3, "api": "csp_WebDAV", "searchable": 1, "changeable": 0, "ext": "./json/webdav.json"}, {"key": "應用商店", "name": "應用商店", "type": 3, "api": "csp_Market", "searchable": 0, "changeable": 0, "ext": "./json/market.json"}], "doh": [{"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}, {"name": "Cloudflare", "url": "https://cloudflare-dns.com/dns-query", "ips": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://dns.adguard.com/dns-query", "ips": ["*************", "*************"]}, {"name": "DNSWatch", "url": "https://resolver2.dns.watch/dns-query", "ips": ["************", "************"]}, {"name": "Quad9", "url": "https://dns.quad9.net/dns-quer", "ips": ["*******", "***************"]}], "rules": [{"name": "proxy", "hosts": ["ddys.pro", "mypikpak.com", ".*workers.dev", "www.cilixiong.com", "*.t4tv.hz.cz", "kuba222.com", "mp4us.com", "dydhhy.com", "magicalsearch.top", "api123.adys.app", "raw.githubusercontent.com", "googlevideo.com", "cdn.v82u1l.com", "cdn.iz8qkg.com", "cdn.kin6c1.com", "c.biggggg.com", "c.olddddd.com", "haiwaikan.com", "www.histar.tv", "youtube.com", "uhibo.com", ".*boku.*", ".*nivod.*", ".*ulivetv.*"]}, {"name": "磁力廣告", "hosts": ["magnet"], "regex": ["更多", "社區", "xuu", "最新", "最新", "直播", "更新", "社区", "有趣", "有趣", "英皇体育", "全中文AV在线", "澳门皇冠赌场", "哥哥快来", "美女荷官", "裸聊", "新片首发", "UUE29"]}, {"name": "海外看", "hosts": ["haiwaikan"], "regex": ["8.16", "8.1748", "10.0099", "10.3333", "10.85", "12.33", "16.0599"]}, {"name": "索尼", "hosts": ["suonizy"], "regex": ["15.1666", "15.2666"]}, {"name": "暴風", "hosts": ["bfzy"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "星星", "hosts": ["aws.ulivetv.net"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "量子", "hosts": ["vip.lz", "hd.lz", "v.cdnlz"], "regex": ["18.5333"]}, {"name": "非凡", "hosts": ["vip.ffzy", "hd.ffzy"], "regex": ["25.1"]}, {"name": "火山嗅探", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "抖音嗅探", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "農民嗅探", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "七新嗅探", "hosts": ["api.52wyb.com"], "regex": ["m3u8?pt=m3u8"]}, {"name": "夜市點擊", "hosts": ["yeslivetv.com"], "script": ["document.getElementsByClassName('vjs-big-play-button')[0].click()"]}, {"name": "毛驢點擊", "hosts": ["www.maolvys.com"], "script": ["document.getElementsByClassName('swal-buttonswal-button--confirm')[0].click()"]}], "ads": ["static-mozai.4gtv.tv", "s3t3d2y8.afcdn.net"]}