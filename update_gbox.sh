#!/bin/bash
# shellcheck shell=bash
# shellcheck disable=SC2086

PATH=${PATH}:/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin:~/bin:/opt/homebrew/bin
export PATH

Blue="\033[1;34m"
Green="\033[1;32m"
Red="\033[1;31m"
Yellow="\033[1;33m"
NC="\033[0m"
INFO="[${Green}INFO${NC}]"
ERROR="[${Red}ERROR${NC}]"
WARN="[${Yellow}WARN${NC}]"

function INFO() {
    echo -e "${INFO} ${1}"
}
function ERROR() {
    echo -e "${ERROR} ${1}"
}
function WARN() {
    echo -e "${WARN} ${1}"
}

if [ -S /var/run/docker.sock ]; then
    auto_update=$(sqlite3 /opt/alist/data/data.db "SELECT value FROM x_setting_items WHERE key='auto_update';")
    if [ "$auto_update" = "true" ]; then
        update_gbox
    fi
fi


update_gbox() {
    list=$(curl -s --unix-socket /var/run/docker.sock http://localhost/containers/json?all=true)
    local_sha=$(curl -s --unix-socket /var/run/docker.sock "http:/localhost/images/ailg/g-box:hostmode/json" | jq -r '.RepoDigests[0]' | cut -f2 -d:)
    remote_sha=$(curl -sSLf https://gbox.ggbond.org/ailg_sha_remote.txt | grep  "ailg/g-box:hostmode" | awk '{print $2}')
    if [ "$local_sha"x != "$remote_sha"x ]; then
        docker_name=$(echo $list | jq -r '.[] | select(.Image == "ailg/g-box:hostmode") | .Names[0] | ltrimstr("/")')
        # 以下两条命令都可以，是用在docker命令中的情况
        # mounts=$(curl -s --unix-socket /var/run/docker.sock "http:/localhost/containers/${docker_name}/json" | jq -r '.Mounts[] | select(.Name == null) | "-v \(.Source):\(.Destination)"' | tr '\n' ' ')
        # mounts=$(echo "$list" | jq -r '.[] | select(.Image == "ailg/g-box:hostmode") | .Mounts[] | select(.Name == null) | "-v \(.Source):\(.Destination)"' | tr '\n' ' ')
        container_binds_str=$(curl -s --unix-socket /var/run/docker.sock "http:/localhost/containers/${docker_name}/json" | jq -r '.HostConfig.Binds[]')
        container_binds=()
        while IFS= read -r bind; do
            container_binds+=("$bind")
        done < <(echo "$container_binds_str" | tr ' ' '\n')

        MOUNTS_FILE="/data/diy_mount.txt"
        
        if [ -f "$MOUNTS_FILE" ]; then
            [ "$(tail -c1 "$MOUNTS_FILE" | od -An -t x1)" != " 0a" ] && echo >> "$MOUNTS_FILE"
            declare -a MOUNTS
            while IFS= read -r line; do
                host_dir=$(echo "$line" | awk '{print $1}')
                container_dir=$(echo "$line" | awk '{print $2}')
                mount_str="$host_dir:$container_dir"
                MOUNTS+=("$mount_str")
            done < "$MOUNTS_FILE"
            MOUNTS_STR=$(IFS=,; printf '"%s",' "${MOUNTS[@]}")
            MOUNTS_STR=${MOUNTS_STR%,}
            response=$(curl -s --unix-socket /var/run/docker.sock \
                -X POST "http://localhost/containers/create" \
                -H "Content-Type: application/json" \
                -d '{
                    "Image": "alpine",
                    "Cmd": ["echo", "Directories are valid."],
                    "HostConfig": {
                    "Binds": ['$MOUNTS_STR']
                    }
                }')
            container_id=$(echo "$response" | jq -r '.Id')


            if curl -s --unix-socket /var/run/docker.sock -X POST "http://localhost/containers/${container_id}/start"; then
                for bind in "${container_binds[@]}"; do
                    if [[ ! " ${MOUNTS[@]} " =~ " $bind " ]]; then
                        MOUNTS+=("$bind")
                    fi
                done
                MOUNTS_OUT=$(IFS=' '; printf '"%s",' "${MOUNTS[@]}")
                MOUNTS_OUT=${MOUNTS_OUT%,}
                echo "${MOUNTS_OUT}" > /data/mounts.bind
            else
                ERROR "您的diy_mount.txt文件中存在不正确的内容，请检查书写格式或宿主机目录是否存在！"
                MOUNTS_OUT=$(IFS=' '; printf '"%s",' "${container_binds[@]}")
                MOUNTS_OUT=${MOUNTS_OUT%,}
                echo "${MOUNTS_OUT}" > /data/mounts.bind
            fi
            curl -s --unix-socket /var/run/docker.sock -X DELETE "http://localhost/containers/${container_id}?force=true"
        else
            MOUNTS_OUT=$(IFS=' '; printf '"%s",' "${container_binds[@]}")
            MOUNTS_OUT=${MOUNTS_OUT%,}
            echo "${MOUNTS_OUT}" > /data/mounts.bind
        fi
        gbox_data_dir=$(curl -s --unix-socket /var/run/docker.sock "http:/localhost/containers/${docker_name}/json" | jq -r '.Mounts[] | select(.Destination == "/data") | .Source')
        response=$(curl -s -X POST \
            --unix-socket /var/run/docker.sock \
            "http:/localhost/containers/create?name=update-gbox" \
            -H "Content-Type: application/json" \
            -d '{
                "Image": "ailg/ggbond:latest",
                "Cmd": ["update_gbox"],
                "HostConfig": {
                    "NetworkMode": "host",
                    "Privileged": true,
                    "Binds": ["'"${gbox_data_dir}"':/update_gbox","/var/run/docker.sock:/var/run/docker.sock"]
                }
            }')
        # container_id=$(echo "$response" | jq -r '.Id')
        chmod 777 /usr/bin/update_gbox && tar -cf /tmp/update_gbox.tar -C /usr/bin update_gbox
        curl -s -X PUT \
            --unix-socket /var/run/docker.sock \
            -H "Content-Type: application/x-tar" \
            --data-binary @/tmp/update_gbox.tar \
            "http://localhost/containers/update-gbox/archive?path=/usr/bin"

        cp -f /opt/alist/data/data.db /data/data.db

        INFO "正在执行g-box容器更新……"
        curl -s --unix-socket /var/run/docker.sock -X POST "http://localhost/containers/update-gbox/start"
    fi 
}

update_gbox(){
    list=$(curl -s --unix-socket /var/run/docker.sock http://localhost/containers/json?all=true)
    docker_name=$(echo $list | jq -r '.[] | select(.Image == "ailg/g-box:hostmode") | .Names[0] | ltrimstr("/")')
    curl -s --unix-socket /var/run/docker.sock -X DELETE "http://localhost/containers/${docker_name}?force=true"
    curl -X DELETE --unix-socket /var/run/docker.sock "http://localhost/images/ailg/g-box:hostmode?force=true"
    curl --unix-socket /var/run/docker.sock -X POST "http://localhost/images/create?fromImage=ailg/g-box:hostmode"
    MOUNTS_STR=$(cat /update_gbox/mounts.bind)
    curl -s --unix-socket /var/run/docker.sock \
        -X POST "http://localhost/containers/create?name=g-box" \
        -H "Content-Type: application/json" \
        -d '{
            "Image": "ailg/g-box:hostmode",
            "HostConfig": {
                "NetworkMode": "host",
                "Binds": ['$MOUNTS_STR']
            }
        }'
    chmod 777 /update_gbox/data.db && tar -cf /tmp/data.tar -C /update_gbox data.db
    curl -X PUT \
        --unix-socket /var/run/docker.sock \
        -H "Content-Type: application/x-tar" \
        --data-binary @/tmp/data.tar \
        "http://localhost/containers/gbox/archive?path=/opt/alist/data"
    curl -s --unix-socket /var/run/docker.sock -X POST "http://localhost/containers/gbox/start"
    rm -f /update_gbox/mounts.bind /update_gbox/data.db*
    curl -s --unix-socket /var/run/docker.sock -X DELETE "http://localhost/containers/update-gbox?force=true"
}

response=$(curl -X POST \
            --unix-socket /var/run/docker.sock \
            "http:/localhost/containers/create?name=update-gbox" \
            -H "Content-Type: application/json" \
            -d '{
                "Image": "ailg/ggbond:latest",
                "Cmd": ["test_ailg"],
                "HostConfig": {
                    "NetworkMode": "host",
                    "Privileged": true,
                    "Binds": ["'"${gbox_data_dir}"':/update_gbox"]
                }
            }')

curl -X PUT \
     --unix-socket /var/run/docker.sock \
     -H "Content-Type: application/octet-stream" \
     --data-binary @"/data/mounts.bind" \
     "http://localhost/containers/update-gbox/archive?path=/usr/bin/mounts.bind"

curl -s --unix-socket /var/run/docker.sock -X POST "http:/v1.41/containers/update-gbox/copy" \
    -H "Content-Type: application/json" \
    -d '{
        "Resource": "/update_gbox/data.db",
        "HostPath": "/opt/alist/data/data.db"
    }'