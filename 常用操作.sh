curl -s -m 20 "https://hub.docker.com/v2/repositories/ailg/g-box/tags/hostmode" | jq -r '.digest' | awk -F ':' '{print $2}'
curl -s --unix-socket /var/run/docker.sock "http://localhost/images/ailg/g-box:hostmode/json" | jq -r '.RepoDigests[0]' | cut -f2 -d:

docker build --no-cache -t ailg/g-box:hostmode -f Dockerfile_debug .

docker run -d --name g-box --net host -v /volume6/test/32:/data -v /volume6/test/32/data:/www/data -v /var/run/docker.sock:/var/run/docker.sock ailg/g-box:hostmode

refresh_token=$(curl -X POST "http://127.0.0.1:5234/api/auth/login" \
-H "Content-Type: application/json" \
-d "{\"username\":\"atv\",\"password\":\"fVWVqfoBnIOl\"}" | jq -r '.data.token')

#以下两个不确定哪个是对的：
curl -X POST "http://127.0.0.1:5234/api/admin/storage/update" \
  -H "Authorization: $refresh_token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 12315,
    "mount_path": "/ailg_jf/115",
    "enable_sign": false,
    "addition": {
      "cookie": "xxx",
      "root_folder_id": "3108093278126341429",
      "qrcode_token": "",
      "qrcode_source": "linux",
      "page_size": 1500,
      "limit_rate": 2,
      "share_code": "swh1iv8335x",
      "receive_code": "ailg"
    }
  }'
  
curl -X POST "http://127.0.0.1:5234/api/admin/storage/update" \
  -H "Authorization: $refresh_token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 12315,
    "mount_path": "/ailg_jf/115",
    "driver": "115 Share",
    "enable_sign": false,
    "addition": "{\"cookie\":\"xxx\",\"root_folder_id\":\"3108093278126341429\",\"qrcode_token\":\"\",\"qrcode_source\":\"linux\",\"page_size\":1500,\"limit_rate\":2,\"share_code\":\"swh1iv8335x\",\"receive_code\":\"ailg\"}"
  }'



  重置内置sun-panel密码的命令
  docker exec g-box sqlite3 /app/conf/database/database.db "UPDATE user SET password = '579646aad11fae4dd295812fb4526245' WHERE id = 1;"