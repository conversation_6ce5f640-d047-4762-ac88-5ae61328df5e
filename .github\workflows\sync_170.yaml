name: Sync to VPS

on:
  push:
    paths:
      - 'sync_files/**'  # 与 sync_giteehub.yaml 使用相同的触发路径
  workflow_dispatch:  # 保留手动触发选项
  workflow_run:
    workflows: ["Update Xiaoya Data Files"]
    types:
      - completed

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.VPS_170_SSH_KEY }}

      - name: Add VPS to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ************** >> ~/.ssh/known_hosts

      - name: Sync files to VPS
        run: |
          # 直接同步 sync_files 目录下的所有文件
          rsync -av sync_files/ root@**************:/var/www/ailg
