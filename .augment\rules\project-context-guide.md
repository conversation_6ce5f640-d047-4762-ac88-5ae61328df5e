# G-Box Pro 项目上下文指南

## 项目背景
G-Box Pro 是一个基于 Java Spring Boot 后端和 Vue.js 前端的网盘管理工具，主要功能包括：
- 网盘资源索引和管理
- 媒体内容分类和展示
- 用户授权和访问控制
- 定时任务和自动化处理

## 技术栈
- 后端：Java 11, Spring Boot 2.7, JPA/Hibernate
- 前端：Vue 3, TypeScript, Element Plus
- 数据库：MySQL 8.0
- 部署：Docker, Docker Compose

## 命名约定
- 所有前端组件使用 PascalCase 命名（如 UserProfile.vue）
- API 端点使用 kebab-case（如 /api/user-settings）
- 数据库表使用 snake_case（如 user_preferences）

## 代码生成要求
- 生成的代码必须包含完整的错误处理
- 所有 API 端点必须有权限控制
- 前端组件必须支持响应式设计
- 所有用户输入必须经过验证

请在所有回答中考虑这些项目上下文信息，不需要在每次回答中重复这些信息。