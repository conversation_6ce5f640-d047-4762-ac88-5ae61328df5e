#!/bin/bash
mirrors=("docker.io" "docker.fxxk.dedyn.io" "docker.m.daocloud.io" "docker.adysec.com" "registry-docker-hub-latest-9vqc.onrender.com" "docker.chenby.cn" "dockerproxy.com" "hub.uuuadc.top" "docker.jsdelivr.fyi" "docker.registry.cyou" "dockerhub.anzu.vip")
for n in {1..3}; do
	echo "第${n}次测试数据："
	for i in "${!mirrors[@]}"; do
	    output=$(
	        curl -s -o /dev/null -w '%{time_total}' --head --request GET "${mirrors[$i]}"
	        echo $? > /tmp/curl_exit_status_${i} &
	    )
	    status[$i]=$!
	    delays[$i]=$(printf "%.2f" $output)
	    printf "Mirror: %-50s Delay: %-10s Exit Status: %s\n" "${mirrors[$i]}" "${delays[$i]}" "$(cat /tmp/curl_exit_status_${i})"
	done
done

