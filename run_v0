#!/usr/bin/with-contenv sh
# shellcheck shell=sh
# shellcheck disable=SC2114

if [ -f /media.img ]; then
    chmod 777 /media.img
	if [ ! -d /volume_img ]; then
        mkdir /volume_img
    fi
    if grep -qs '/volume_img' /proc/mounts; then
        umount /volume_img
        wait ${!}
    fi
    losetup -o 10000000 /dev/loop7 /media.img 
	mount /dev/loop7 /volume_img
	wait ${!}
    echo "img 镜像挂载成功！"
    if [ -d /media ]; then
		if ! rm -rf /media; then
		    echo '删除/media失败！使用老G速装版emby请勿将任何目录挂载到容器的/media目录！程序退出！'
            exit 1
        fi
    fi
    ln -sf /volume_img/xiaoya /media
	if sed -i 's/ \/config/ \/volume_img\/config/' /etc/services.d/emby-server/run; then
	    echo "replace emby's config successed"
    fi
else
    echo "img 镜像未挂载，跳过自动挂载！"
fi
