{"spider": "./jar/fan.txt;md5;dd909349c70fe92e82470eba611b7094", "lives": [{"name": "初秋语•ipv4", "type": 0, "url": "./list.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "YanG•综合", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/YanG-1989/m3u/main/Gather.m3u", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "drang•ipv6", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/drangjchen/IPTV/main/M3U/ipv6.m3u", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "范明明•ipv6", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/fanmingming/live/main/tv/m3u/ipv6.m3u", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "<PERSON><PERSON><PERSON><PERSON>•综合", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/YueChan/Live/main/IPTV.m3u", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "Yoursmile•综合", "type": 0, "url": "https://agit.ai/Yoursmile7/TVBox/raw/branch/master/live.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "饭太硬•综合", "type": 0, "url": "https://agit.ai/fantaiying/0/raw/branch/main/tvlive.txt", "playerType": 1, "ua": "okhttp/3.15", "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "肥猫•综合", "type": 0, "url": "http://我不是.肥猫.live/TV/tvzb.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "Ray•综合", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/dxawi/0/main/tvlive.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "俊于•综合", "type": 0, "url": "http://home.jundie.top:81/Cat/tv/live.txt", "playerType": 1, "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "初秋语•电台", "type": 0, "url": "./radio.txt", "playerType": 1}, {"name": "范明明•电台", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/fanmingming/live/main/radio/m3u/index.m3u", "playerType": 1}, {"name": "肥羊•斗鱼", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/Ftindy/IPTV-URL/main/douyuyqk.m3u", "playerType": 1}, {"name": "肥羊•虎牙", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/Ftindy/IPTV-URL/main/huyayqk.m3u", "playerType": 1}, {"name": "YanG•斗鱼", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/YanG-1989/m3u/main/yu.m3u"}, {"name": "YanG•虎牙", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/YanG-1989/m3u/main/ya.m3u"}, {"name": "16万•MV", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/lystv/short/main/影视/tvb/MTV.txt"}, {"name": "YuanHsing•油管", "type": 0, "url": "https://github.moeyy.xyz/https://raw.githubusercontent.com/YuanHsing/YouTube_to_m3u/main/youtube.m3u"}], "wallpaper": "http://饭太硬.top/深色壁纸/api.php", "sites": [{"key": "豆豆", "name": "豆瓣┃搜索", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 0}, {"key": "drpy_js_新片场", "name": "新片场┃新片", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/新片场.js"}, {"key": "csp_YGP", "name": "预告片┃新片", "type": 3, "api": "csp_YGP", "searchable": 1, "quickSearch": 1, "changeable": 0}, {"key": "csp_<PERSON><PERSON>", "name": "哔哩┃合集", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "style": {"type": "rect", "ratio": 1.597}, "filterable": 1, "changeable": 0, "ext": "./json/chuqiuyu.json"}, {"key": "csp_xuexi", "name": "哔哩┃课堂", "type": "3", "api": "csp_<PERSON><PERSON>", "searchable": "0", "quickSearch": "0", "style": {"type": "rect", "ratio": 1.597}, "filterable": "1", "changeable": 0, "ext": "./json/xuexi.json"}, {"key": "玩偶哥哥", "name": "玩偶哥哥┃4K弹幕", "type": 3, "api": "csp_WoGG", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.txt+4k|auto|fhd$$$https://www.wogg.xyz/$$$弹", "timeout": 60}, {"key": "csp_Aid", "name": "急救┃指南", "type": 3, "api": "csp_FirstAid", "searchable": 1, "quickSearch": 1, "changeable": 0, "style": {"type": "rect", "ratio": 3.8}}, {"key": "lf_js_lf_live", "name": "电视┃直播", "type": 3, "api": "./lib/lf_live_min.js", "style": {"type": "oval"}, "searchable": 1, "changeable": 0, "quickSearch": 1, "filterable": 1, "ext": "./js/lf_live.txt"}, {"key": "drpy_js_ikanbot3", "name": "爱看机器人┃聚合", "type": 3, "api": "./lib/drpy2.min.js", "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "./js/ikanbot3.js", "timeout": 60}, {"key": "T4-duanju5", "name": "短剧屋┃聚合", "type": 4, "api": "https://catbox.n13.club/duanju5.php", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1, "ext": true, "timeout": 60}, {"key": "csp_xBPQ_短剧侠", "name": "短剧侠┃聚合", "type": 3, "api": "csp_XBPQ", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1, "ext": "./XBPQ/短剧侠.json", "jar": "./jar/XBPQ1.jar;md5;56be033d13e13803dc999d881437d1f6", "timeout": 60}, {"key": "看球", "name": "看球┃体育", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 1, "changeable": 0, "style": {"type": "list"}, "timeout": 30}, {"key": "88js", "name": "88看球┃体育", "type": 3, "api": "./lib/drpy.min.js", "ext": "./js/88看球.js", "style": {"type": "list"}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "drpy_js_310直播", "name": "310直播┃体育", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "style": {"type": "list"}, "ext": "./js/310直播.js"}, {"key": "csp_xp_qiumi", "name": "Jrs球迷┃体育", "type": 3, "api": "csp_XPath", "style": {"type": "list"}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./json/dj看球.json"}, {"key": "csp_XPath_企鹅体育", "name": "企鹅┃体育", "type": 3, "api": "csp_XPath", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./json/企鹅直播.json"}, {"key": "短剧", "name": "上头┃短剧", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "酷看", "name": "酷看┃秒播", "type": 3, "api": "csp_<PERSON><PERSON>s", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "苹果", "name": "小苹果┃复活", "type": 3, "api": "csp_LiteApple", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "原创", "name": "原创┃无广", "type": 3, "api": "csp_YCyz", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "<PERSON><PERSON><PERSON>", "name": "视觉┃直连", "type": 3, "api": "csp_<PERSON><PERSON>j", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://www.80yy3.com/"}, {"key": "厂长", "name": "厂长┃无广", "type": 3, "api": "csp_Czsapp", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://www.czzy77.com/"}, {"key": "77", "name": "七七┃秒播", "type": 3, "api": "csp_Kunyu77", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "白嫖", "name": "白飘┃无广", "type": 3, "api": "csp_<PERSON>", "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "DiDuan", "name": "低端┃外剧", "type": 3, "api": "csp_Ddrk", "playerType": "2", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "南瓜", "name": "南瓜┃App", "type": 3, "api": "csp_<PERSON>", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "毛驴", "name": "毛驴┃直连", "type": 3, "api": "csp_MLYS", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://www.maolvys.com/"}, {"key": "萌米", "name": "萌米┃App", "type": 3, "api": "csp_AppTT", "timeout": 15, "playerType": 1, "ext": "AO7TcBkd8Iifux0Y8Qze6tVlMg=="}, {"key": "zxzj", "name": "在线┃外剧", "type": 3, "api": "csp_Zxzj", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://www.zxzja.com/"}, {"key": "一起看 ", "name": "一起┃App", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "csp_<PERSON><PERSON><PERSON>", "name": "农民┃直连", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "./json/nmys.json"}, {"key": "csp_<PERSON>ttoo", "name": "比特┃秒播", "type": 3, "api": "csp_<PERSON><PERSON>woo", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Lib", "name": "立播┃直连", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://www.libvio.pw/"}, {"key": "Auete", "name": "奥特┃直连", "type": 3, "api": "csp_Auete", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://auete.pro/"}, {"key": "SP33", "name": "三三┃解析", "type": 3, "api": "csp_SP33", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 0}, {"key": "贱贱", "name": "贱贱┃p2p", "type": 3, "searchable": 1, "quickSearch": 1, "changeable": 1, "playerType": "1", "api": "./lib/drpy2.min.js", "ext": "./js/荐片.js"}, {"key": "csp_Dm84", "name": "动漫┃巴士", "type": 3, "api": "csp_Dm84", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_<PERSON>", "name": "樱花┃动漫", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_Ysj", "name": "异界┃动漫", "type": 3, "api": "csp_Ysj", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_Anime1", "name": "日本┃动漫", "type": 3, "api": "csp_Anime1", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_Yj1211", "name": "网红┃直播", "type": 3, "api": "csp_Yj1211", "playerType": "1", "searchable": 0, "quickSearch": 1, "changeable": 0}, {"key": "csp_<PERSON>bys", "name": "泥巴┃飞", "type": 3, "api": "csp_Ni<PERSON>i", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_trj", "name": "唐人街┃飞", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "YiSo", "name": "易搜┃搜索", "type": 3, "api": "csp_<PERSON>o", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.txt+4k|auto|fhd", "timeout": 60}, {"key": "<PERSON><PERSON>", "name": "找资源┃搜索", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.txt+4k|auto|fhd$$$zhaoziyuan777$$$qqq111", "timeout": 60}, {"key": "PanSou", "name": "盘搜┃搜索", "type": 3, "api": "csp_Pan<PERSON>ou", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.txt+4k|auto|fhd", "timeout": 60}, {"key": "UpYun", "name": "Up搜┃搜索", "type": 3, "api": "csp_UpYun", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.txt+4k|auto|fhd", "timeout": 60}, {"key": "PanSearch", "name": "盘Se┃搜索", "type": 3, "api": "csp_PanSearch", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/TV/token.txt+4k|auto|fhd", "timeout": 60}, {"key": "push_agent", "name": "手机┃推送", "type": 3, "api": "csp_<PERSON>ush", "searchable": 0, "quickSearch": 0, "ext": "http://127.0.0.1:9978/file/TV/token.txt+4k|auto|fhd", "timeout": 60}, {"key": "csp_AList", "name": "AList┃网盘", "type": "3", "api": "csp_AList", "searchable": "0", "quickSearch": "0", "filterable": "1", "changeable": 0, "ext": "./json/alist.json"}, {"key": "新6V", "name": "新6V磁力┃慎用", "type": 3, "api": "csp_SixV", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://www.xb6v.com/", "timeout": 60}, {"key": "百度", "name": "百度┃采集", "type": 1, "api": "https://api.apibdzy.com/api.php/provide/vod?ac=list", "searchable": 1, "filterable": 0, "categories": ["国产动漫", "日韩动漫", "大陆剧", "欧美剧", "韩剧", "日剧", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片"]}, {"key": "量子", "name": "量子┃采集", "type": 0, "api": "https://cj.lziapi.com/api.php/provide/vod/at/xml/", "searchable": 1, "changeable": 1, "categories": ["国产动漫", "日韩动漫", "国产剧", "韩国剧", "日本剧", "电影片", "连续剧", "综艺片", "动漫片", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片", "台湾剧", "香港剧", "欧美剧", "记录片", "海外剧", "泰国剧", "大陆综艺", "港台综艺", "日韩综艺", "欧美综艺", "欧美动漫", "港台动漫", "海外动漫", "体育", "足球", "篮球", "网球", "斯诺克"]}, {"key": "非凡", "name": "非凡┃采集", "type": 0, "api": "http://cj.ffzyapi.com/api.php/provide/vod/at/xml/", "searchable": 1, "changeable": 1, "categories": ["国产动漫", "日韩动漫", "国产剧", "韩国剧", "日本剧", "电影片", "连续剧", "综艺片", "动漫片", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片", "香港剧", "欧美剧", "记录片", "台湾剧", "海外剧", "泰国剧", "大陆综艺", "港台综艺", "日韩综艺", "欧美综艺", "欧美动漫", "港台动漫", "海外动漫"]}, {"key": "haiwaikan", "name": "海外看┃采集", "type": 1, "api": "https://haiwaikan.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "暴風", "name": "暴風┃采集", "type": 1, "api": "https://bfzyapi.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "索尼", "name": "索尼┃采集", "type": 1, "api": "https://suoniapi.com/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "快帆", "name": "快帆┃采集", "type": 1, "api": "https://api.kuaifan.tv/api.php/provide/vod", "searchable": 1, "changeable": 1}, {"key": "drpy_js_360影视", "name": "官源┃360[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/360影视.js"}, {"key": "drpy_js_奇珍异兽", "name": "官源┃爱奇艺[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/奇珍异兽.js"}, {"key": "drpy_js_百忙无果", "name": "官源┃芒果[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/百忙无果.js"}, {"key": "drpy_js_腾云驾雾", "name": "官源┃腾讯[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/腾云驾雾.js"}, {"key": "drpy_js_菜狗", "name": "官源┃搜狗[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/菜狗.js"}, {"key": "drpy_js_优酷", "name": "官源┃优酷[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/优酷.js"}, {"key": "drpy_js_我的哔哩", "name": "弹幕┃我的哔哩[js]", "type": 3, "api": "./lib/drpy2.min.js", "style": {"type": "rect", "ratio": 1.597}, "changeable": 0, "ext": "./js/我的哔哩.js"}, {"key": "drpy_js_哔哩直播", "name": "弹幕┃哔哩直播[js]", "type": 3, "api": "./lib/drpy2.min.js", "style": {"type": "rect", "ratio": 1.597}, "changeable": 0, "ext": "./js/哔哩直播.js"}, {"key": "drpy_js_JustLive", "name": "直播┃JustLive[js]", "type": 3, "api": "./lib/drpy2.min.js", "style": {"type": "rect", "ratio": 1.597}, "changeable": 0, "ext": "./js/JustLive.js"}, {"key": "drpy_js_斗鱼直播", "name": "直播┃斗鱼[js]", "type": 3, "api": "./lib/drpy2.min.js", "style": {"type": "rect", "ratio": 1.597}, "changeable": 0, "ext": "./js/斗鱼直播.js"}, {"key": "drpy_js_虎牙直播", "name": "直播┃虎牙[js]", "type": 3, "api": "./lib/drpy2.min.js", "style": {"type": "rect", "ratio": 1.597}, "changeable": 0, "ext": "./js/虎牙直播.js"}, {"key": "drpy_js_童趣", "name": "少儿┃童趣[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/童趣.js"}, {"key": "drpy_js_兔小贝", "name": "少儿┃兔小贝[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/兔小贝.js"}, {"key": "drpy_js_AnFuns", "name": "动漫┃AnFuns[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/AnFuns.js"}, {"key": "drpy_js_NT动漫", "name": "动漫┃NT动漫[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/NT动漫.js"}, {"key": "drpy_js_<PERSON><PERSON><PERSON>", "name": "动漫┃NyaFun[js]", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/NyaFun.js"}, {"key": "drpy_js_听书网", "name": "听书┃听书网[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/听书网.js"}, {"key": "drpy_js_喜马拉雅", "name": "听书┃喜马拉雅[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/喜马拉雅.js"}, {"key": "drpy_js_评书随身听", "name": "评书┃评书随身听[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/评书随身听.js"}, {"key": "drpy_js_相声随身听", "name": "相声┃相声随身听[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/相声随身听.js"}, {"key": "drpy_js_好趣网", "name": "电视┃好趣网[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "style": {"type": "oval", "ratio": 1.1}, "ext": "./js/好趣网.js"}, {"key": "drpy_js_广播迷FM", "name": "广播┃广播迷FM[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/广播迷FM.js"}, {"key": "drpy_js_蜻蜓FM", "name": "广播┃蜻蜓FM[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/蜻蜓FM.js"}, {"key": "drpy_js_DJ音乐", "name": "音频┃DJ音乐[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/DJ音乐.js"}, {"key": "drpy_js_短视频", "name": "聚合┃短视频[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/短视频.js"}, {"key": "drpy_js_酷6网", "name": "聚合┃酷6网[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/酷6网.js"}, {"key": "drpy_js_网易公版影像", "name": "聚合┃网易公版[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/网易公版影像.js"}, {"key": "drpy_js_酷奇MV", "name": "MV┃酷奇[js]", "type": 3, "api": "./lib/drpy2.min.js", "changeable": 0, "ext": "./js/酷奇MV.js"}, {"key": "csp_xBPQ_来看", "name": "影视┃耐看[XBPQ]", "type": 3, "api": "csp_XBPQ", "ext": "./XBPQ/来看.json"}, {"key": "csp_xBPQ_星辰", "name": "影视┃星辰[XBPQ]", "type": 3, "api": "csp_XBPQ", "playerType": "1", "ext": "./XBPQ/星辰.json"}, {"key": "csp_xBPQ_素白", "name": "影视┃素白[XBPQ]", "type": 3, "api": "csp_XBPQ", "playerType": "1", "ext": "./XBPQ/素白.json"}, {"key": "csp_XYQBiu_1985电影网", "name": "影视┃1985电影网[XYQ]", "type": 3, "api": "csp_XYQBiu", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./XYQBiu/1985电影网.json"}, {"key": "csp_XYQBiu_大师兄影视", "name": "影视┃大师兄影视[XYQ]", "type": 3, "api": "csp_XYQBiu", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./XYQBiu/大师兄影视.json"}, {"key": "csp_XYQBiu_七小时影院", "name": "影视┃七小时影院[XYQ]", "type": 3, "api": "csp_XYQBiu", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./XYQBiu/七小时影院.json"}, {"key": "csp_XYQBiu_TVB云播", "name": "影视┃TVB云播[XYQ]", "type": 3, "api": "csp_XYQBiu", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./XYQBiu/TVB云播.json"}, {"key": "csp_XYQBiu_骚火电影VIP", "name": "影视┃骚火•飞[XYQ]", "type": 3, "api": "csp_XYQBiu", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./XYQBiu/骚火电影VIP.json"}, {"key": "csp_XYQBiu_Auete影视", "name": "影视┃Auete•飞[XYQ]", "type": 3, "api": "csp_XYQBiu", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./XYQBiu/Auete影视.json"}, {"key": "csp_xb_zbk", "name": "影视┃真不卡[XBiu]", "type": 3, "api": "csp_XBiu", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "https://www.zbkk.net/vodshow/{cateId}--------{catePg}---.html"}, {"key": "csp_xb_ysgc", "name": "影视┃影视工厂[XBiu]", "type": 3, "api": "csp_XBiu", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "https://www.ysgc.fun/vodtype/{cateId}-{catePg}.html"}, {"key": "csp_xb_cokemv", "name": "影视┃cokemv[XBiu]", "type": 3, "api": "csp_XBiu", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "https://cokemv.me/vodshow/{cateId}--------{catePg}---.html"}, {"key": "csp_xb_达达龟", "name": "影视┃达达龟[XBiu]", "type": 3, "api": "csp_XBiu", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "https://www.dadagui.me/vodshow/{cateId}--------{catePg}---.html"}, {"key": "csp_xb_shm", "name": "影视┃神马影视[XBiu]", "type": 3, "api": "csp_XBiu", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "https://www.smdyy.cc/show/{cateId}-{area}--{class}-----{catePg}---{year}.html"}, {"key": "csp_xb_fg", "name": "影视┃疯狗影视[XBiu]", "type": 3, "api": "csp_XBiu", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "https://m.fenggoudy3.com/list-select-id-{cateId}-type--area--year--star--state--order-addtime-p-{catePg}.html"}, {"key": "csp_XP_动漫巴士", "name": "影视┃动漫巴士[XPF]", "type": 3, "api": "csp_XPathFilter", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./json/动漫巴士.json"}, {"key": "csp_XP_dmw", "name": "影视┃动漫岛[XPMF]", "type": 3, "api": "csp_XPathMacFilter", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./json/dmw.json"}, {"key": "csp_XP_gongreng", "name": "影视┃工人影视[XPMF]", "type": 3, "api": "csp_XPathMacFilter", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./json/gongreng.json"}, {"key": "csp_XP_独播库", "name": "影视┃独播库•飞[XPMF]", "type": 3, "api": "csp_XPathMacFilter", "playerType": 0, "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./json/duboku.json"}, {"key": "bb", "name": "配置接口完全免费", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/drpy.js"}, {"key": "cc", "name": "请勿相信视频中任何广告", "type": 3, "api": "./lib/drpy2.min.js", "ext": "./js/drpy.js"}], "parses": [{"name": "Json聚合", "type": 3, "url": "Demo"}, {"name": "飞云", "type": 1, "url": "https://daina.hk/api/?key=e1e84ecae847b6e4a1a27fd4611f45f5&url=", "ext": {"flag": ["FYNB", "mgtv", "qiyi", "imgo", "爱奇艺", "奇艺", "qq", "腾讯", "youku", "优酷", "pptv", "PPTV", "letv", "乐视", "leshi", "bilibili", "哔哩哔哩", "哔哩", "mgtv", "芒果"], "header": {"User-Agent": "okhttp/4.1.0"}}}, {"name": "米饭", "type": 1, "url": "https://api.json.icu/api/?key=b1f3b1a9e1c0a42ae969db10bd315a95&url=", "ext": {"flag": ["爱奇艺", "奇艺", "qq", "腾讯", "youku", "优酷", "pptv", "PPTV", "letv", "乐视", "leshi", "bilibili", "哔哩哔哩", "哔哩", "mgtv", "芒果", "芒果"], "header": {"User-Agent": "okhttp/4.1.0"}}}, {"name": "饭饭", "type": 1, "url": "http://api.888484.xyz/神秘哥哥/super.php?v=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "tucheng", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "tnmb", "seven", "yzm", "<PERSON><PERSON><PERSON>", "R<PERSON><PERSON><PERSON><PERSON>", "bilibili", "1905", "xinvip", "XAL", "qiqi", "XALS", "Yu<PERSON>i-vip"]}}, {"name": "虾米", "type": 0, "url": "https://jx.xmflv.com/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "imgo", "rx", "ltnb", "bilibili", "1905", "xigua"]}}, {"name": "PM", "url": "https://www.playm3u8.cn/jiexi.php?url=", "type": 0, "ext": {"flag": ["qiyi", "imgo", "爱奇艺", "奇艺", "qq", "腾讯", "youku", "优酷", "pptv", "PPTV", "letv", "乐视", "leshi", "bilibili", "哔哩哔哩", "哔哩", "mgtv", "芒果", "sohu", "xigua", "fun", "风行"], "header": {"User-Agent": "Mozilla/5.0"}}, "header": {"User-Agent": "Mozilla/5.0"}}, {"name": "全民", "url": "http://api.wpsseo.cn/?v=", "type": 0, "ext": {"flag": ["qiyi", "imgo", "爱奇艺", "奇艺", "qq", "腾讯", "youku", "优酷", "pptv", "PPTV", "letv", "乐视", "leshi", "bilibili", "哔哩哔哩", "哔哩", "mgtv", "芒果", "sohu", "xigua", "fun", "风行"], "header": {"User-Agent": "Mozilla/5.0"}}, "header": {"User-Agent": "Mozilla/5.0"}}, {"name": "m3u8", "type": 0, "url": "https://jx.m3u8.tv/jiexi/?url="}, {"name": "8090", "url": "https://www.8090.la/8090/?url=", "type": 0, "ext": {"flag": ["qiyi", "imgo", "爱奇艺", "奇艺", "qq", "腾讯", "youku", "优酷", "pptv", "PPTV", "letv", "乐视", "leshi", "bilibili", "哔哩哔哩", "哔哩", "mgtv", "芒果", "sohu", "xigua", "fun", "风行"], "header": {"User-Agent": "Mozilla/5.0"}}, "header": {"User-Agent": "Mozilla/5.0"}}, {"name": "看看", "type": 0, "url": "https://jx.m3u8.pw/?url="}, {"name": "巧技", "type": 1, "url": "http://pandown.pro/app/kkdy.php?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "rx", "ltnb", "bilibili", "1905", "xigua"]}}, {"name": "左岸", "type": 1, "url": "https://api.tyun77.cn/api.php/provide/parseDicturl?url=", "ext": {"flag": ["爱奇艺", "奇艺", "qq", "腾讯", "youku", "优酷", "pptv", "PPTV", "letv", "乐视", "leshi", "bilibili", "哔哩哔哩", "哔哩", "mgtv", "芒果", "芒果"], "header": {"User-Agent": "okhttp/3.12.0"}}}, {"name": "世界", "type": 1, "url": "http://***************:84/api/?key=f3913eb3f85a8298b3e6e427b8712b2e&url=", "ext": {"flag": ["qq", "mgtv", "Yu<PERSON>i-vip"]}}, {"name": "咸鱼", "type": 0, "url": "https://jx.xyflv.cc/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0(Linux;Android13;V2049ABuild/TP1A.220624.014;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/116.0.0.0MobileSafari/537.36", "referer": "https://www.xyflv.cc/"}}}, {"name": "云解析", "type": 0, "url": "https://jx.yparse.com/index.php?url=", "ext": {"header": {"user-agent": "Mozilla/5.0(Linux;Android13;V2049ABuild/TP1A.220624.014;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/116.0.0.0MobileSafari/537.36"}}}, {"name": "爱豆", "type": 0, "url": "https://jx.aidouer.net/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0(Linux;Android13;V2049ABuild/TP1A.220624.014;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/116.0.0.0MobileSafari/537.36", "referer": "https://jiejie.uk/"}}}], "flags": ["youku", "优酷", "优 酷", "优酷视频", "qq", "腾讯", "腾 讯", "腾讯视频", "<PERSON><PERSON><PERSON>", "qiyi", "奇艺", "爱奇艺", "爱 奇 艺", "m1905", "xigua", "letv", "leshi", "乐视", "乐 视", "sohu", "搜狐", "搜 狐", "搜狐视频", "tudou", "mgtv", "芒果", "imgo", "芒果TV", "芒 果 T V", "bilibili", "哔 哩", "哔 哩 哔 哩", "SPA", "Yu<PERSON>i-vip", "pptv", "PPTV", "ltnb", "rx", "SLYS4k", "tucheng", "BYGA", "luanzi", "dxzy", "QEYSS", "<PERSON><PERSON><PERSON>", "AliS", "122", "chuang<PERSON>", "CL4K", "x<PERSON><PERSON>", "wuduzy", "wasu", "ren<PERSON><PERSON>", "ppayun", "haiwaikan", "cool", "dbm3u8", "xmm", "funshion", "ruyi1080", "ruyib1080"], "doh": [{"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}, {"name": "Cloudflare", "url": "https://cloudflare-dns.com/dns-query", "ips": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://dns.adguard.com/dns-query", "ips": ["*************", "*************"]}, {"name": "DNSWatch", "url": "https://resolver2.dns.watch/dns-query", "ips": ["************", "************"]}, {"name": "Quad9", "url": "https://dns.quad9.net/dns-quer", "ips": ["*******", "***************"]}], "rules": [{"name": "kk", "hosts": ["kuaikan"], "regex": ["5", "20.123", "20.167", "#EXT-X-DISCONTINUITY\\r*\\n*((?!#EXT-X-DISCONTINUITY)[\\s\\S])*?#EXT-X-KEY:METHOD((?!#EXT-X-DISCONTINUITY)[\\s\\S])*?#EXT-X-DISCONTINUITY"]}, {"name": "yqk", "hosts": ["yqk"], "regex": ["18.4", "15.1666", "#EXT-X-DISCONTINUITY\\r*\\n*((?!#EXT-X-DISCONTINUITY)[\\s\\S])*?#EXT-X-CUE-OUT((?!#EXT-X-DISCONTINUITY)[\\s\\S])*?#EXT-X-CUE-IN"]}, {"name": "sn", "hosts": ["suonizy"], "regex": ["#EXTINF.*?\\s+.*?original.*?\\.ts\\s+", "15.1666", "15.2666", "16.3333", "15.266667"]}, {"name": "bf", "hosts": ["bfzy"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "xx", "hosts": ["aws.ulivetv.net"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "lz", "hosts": ["vip.lz", "hd.lz", "v.cdnlz", "yzzy1.play"], "regex": ["18.5333", "19.52", "18.6666"]}, {"name": "ff", "hosts": ["vip.ffzy", "hd.ffzy", "ffzy"], "regex": ["25.0666", "25.08", "20.52", "25.1", "25.1999"]}, {"name": "hs", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "dy", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "nm", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "cl", "hosts": ["magnet"], "regex": ["最 新", "直 播", "更 新"]}]}