<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用 G-Box</title>
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #121212;
            color: #e0e0e0;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background: #1e1e1e;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
        h1, h2 {
            text-align: center;
            color: #ffffff;
        }
        p {
            margin: 10px 0;
            color: #b0b0b0;
        }
        .code-container {
            position: relative;
            background: #333;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        code {
            background: none;
            padding: 0;
            border-radius: 0;
            color: #e0e0e0;
        }
        .copy-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            color: #3498db;
        }
        .copy-icon:hover {
            color: #2980b9;
        }
        .copy-notification {
            position: absolute;
            top: -20px;
            right: 10px;
            background: #3498db;
            color: #fff;
            padding: 5px;
            border-radius: 4px;
            display: none;
        }
        a {
            color: #3498db;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px 0;
            background-color: #3498db;
            color: #fff;
            border-radius: 5px;
            text-decoration: none;
        }
        .button:hover {
            background-color: #2980b9;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
        }
        img {
            max-width: 200px;
            height: auto;
            border-radius: 8px;
        }
        .card-container {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        .card {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            margin: 0 10px;
            background: #2c2c2c;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        }
        .card img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }
        .card-content {
            margin-top: 10px;
            text-align: center;
        }
        .card-content h3 {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>欢迎使用 G-Box</h1>
        <p>
            G-Box的安装使用说明：<br>
            在哪个设备安装，就用SSH工具登陆它的命令控制台，执行一键脚本，在主菜单选择5，根据引导完成项安装；<br>
            <div class="code-container">
                <code id="script1">bash -c "$(curl -sSLf https://ailg.ggbond.org/xy_install.sh)"</code>
                <span class="copy-icon" onclick="copyToClipboard('#script1')">📋</span>
                <span class="copy-notification" id="copy-notification1">已复制</span>
            </div>
            备用脚本：<br>
            <div class="code-container">
                <code id="script2">bash -c "$(curl -sSLf https://gbox.ggbond.org/xy_install.sh)"</code>
                <span class="copy-icon" onclick="copyToClipboard('#script2')">📋</span>
                <span class="copy-notification" id="copy-notification2">已复制</span>
            </div>
            <div class="code-container">
                <code id="script3">bash -c "$(curl -sSLf https://xy.ggbond.org/xy/xy_install.sh)"</code>
                <span class="copy-icon" onclick="copyToClipboard('#script3')">📋</span>
                <span class="copy-notification" id="copy-notification3">已复制</span>
            </div>
        </p>
        
        <h2>G-box的详细使用说明</h2>
        <div class="card-container">
            <div class="card">
                <img src="https://img.youtube.com/vi/hYwCxJqChUw/0.jpg" alt="G-box 视频教程">
                <div class="card-content">
                    <h3>G-Box保镖级教程</h3>
                    <a href="https://youtu.be/hYwCxJqChUw?si=YZJsw_Aqt10VRNqS" target="_blank" class="button">Youtube观看</a>
                    <a href="https://www.alipan.com/s/KEvj3Em71vU" target="_blank" class="button">转存阿里云盘观看</a>
                </div>
            </div>

            <div class="card">
                <img src="https://pic.imgdb.cn/item/66e0547ad9c307b7e968f395.png" alt="B站 视频教程">
                <div class="card-content">
                    <h3>G-Box解决115风控（含速装Emby教程）</h3>
                    <a href="https://b23.tv/ewhi6pF" target="_blank" class="button">B站观看</a>
                </div>
            </div>
        </div>

        <p>
            问题/玩法/心得……可加TG群求助或交流：
            <a href="https://t.me/ailg666" target="_blank" class="button">加入TG群</a>
        </p>

        <div class="image-container">
            <p>如果您喜欢G-box，可以请老G喝杯咖啡，感谢您的支持！</p>
            <a href="https://smms.app/image/I2sicMHx31DKUdg" target="_blank">
                <img src="https://s2.loli.net/2024/08/08/I2sicMHx31DKUdg.jpg" alt="老G的赞赏码" />
            </a>
        </div>
    </div>

    <script>
        function copyToClipboard(element) {
            var temp = document.createElement("textarea");
            document.body.appendChild(temp);
            temp.value = document.querySelector(element).textContent;
            temp.select();
            document.execCommand("copy");
            document.body.removeChild(temp);
            showNotification(element);
        }

        function showNotification(element) {
            var notification = document.querySelector(element + ' + .copy-icon + .copy-notification');
            notification.style.display = 'inline';
            setTimeout(function() {
                notification.style.display = 'none';
            }, 1000);
        }
    </script>
</body>
</html>
