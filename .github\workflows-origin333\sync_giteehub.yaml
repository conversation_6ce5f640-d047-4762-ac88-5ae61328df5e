name: Sync to GitHub and <PERSON><PERSON><PERSON>

on:
  # push:
  #   paths:
  #     - 'sync_files/**'
  workflow_dispatch:
  # workflow_run:
  #   workflows: ["Update Xiaoya Data Files"]
  #   types:
  #     - completed

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Git credentials
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "ailg666"
          git config --global credential.helper store
          echo "https://${{ secrets.REPO_TOKEN_AILG666 }}:<EMAIL>" > ~/.git-credentials
          echo "https://${{ secrets.GITEE_TOKEN }}:<EMAIL>" >> ~/.git-credentials
      
      - name: Initialize GitHub repository if not exists
        run: |
          mkdir -p xy
          cd xy
          if [ ! -d .git ]; then
            git init
            git remote add origin https://github.com/ailg666/xy.git
            git remote add gitee https://gitee.com/i-xxg/xy.git
            git checkout -b master
          fi

      - name: Pull from GitHub
        run: |
          cd xy
          git pull --rebase origin master

      - name: Copy selected files to GitHub
        run: |
          # 使用 --exclude 排除 .git 目录
          rsync -av --exclude='.git' sync_files/ xy/

      - name: Commit and Push to GitHub and Gitee
        run: |
          cd xy
          git add .
          git commit -m "Sync from GitHub" || echo "No changes to commit"
          
          # 推送到 GitHub 和 Gitee
          git push -f origin master
          git push -f https://i-xxg:${{ secrets.GITEE_TOKEN }}@gitee.com/i-xxg/xy.git master

      # 添加重试机制
      # - name: Retry Push if Failed
      #   if: failure()
      #   run: |
      #     cd xy
      #     git pull --rebase origin master
      #     git push -f origin master
      #     git push -f https://i-xxg:${{ secrets.GITEE_TOKEN }}@gitee.com/i-xxg/xy.git master
