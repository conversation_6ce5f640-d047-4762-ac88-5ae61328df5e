#!/usr/bin/bash
#!/bin/bash
docker run -d --name emby2 -v /etc/nsswitch.conf:/etc/nsswitch.conf -v /mnt/media_rw/d48ded09-158b-4536-b78b-0279c6936327/.ugreen_nas/312373/Docker/xiaoyadata/config:/config -v /mnt/media_rw/d48ded09-158b-4536-b78b-0279c6936327/.ugreen_nas/312373/Docker/xiaoyadata/xiaoya:/media -v /mnt/dm-0/.ugreen_nas/312373/影视:/movie1 -v /mnt/dm-2/.ugreen_nas/312373/影视:/movie2 --user 0:0 --net=host --add-host="xiaoya.host:*************" --restart always emby/embyserver:********