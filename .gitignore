#以下为使用示例：
# 忽略所有的 .a 文件
#*.a

# 但跟踪所有的 lib.a，即便你在前面忽略了 .a 文件
#!lib.a

# 只忽略当前目录下的 TODO 文件，而不忽略 subdir/TODO
#/TODO

# 忽略任何目录下名为 build 的文件夹
#build/

# 忽略 doc/notes.txt，但不忽略 doc/server/arch.txt
#doc/*.txt

# 忽略 doc/ 目录及其所有子目录下的 .pdf 文件
#doc/**/*.pdf

*.[oa]
*.log
*~
t11.sh
t10.sh
t12.sh
t*.sh
gitlab_xy/
xy/

# 先忽略data目录下的文件
data/index.zip
data/update.zip
data/tvbox.zip
data/version.txt

# 忽略根目录下的文件
index.zip
update.zip
tvbox.zip
version.txt

.github/.active_remote

# 例外规则：允许sync_files目录下的文件被跟踪
!sync_files/index.zip
!sync_files/update.zip
!sync_files/tvbox.zip
!sync_files/version.txt

