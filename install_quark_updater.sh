#!/bin/bash

# 颜色定义
Blue="\033[1;34m"
Green="\033[1;32m"
Red="\033[1;31m"
Yellow="\033[1;33m"
NC="\033[0m"

# 提示函数
INFO() { echo -e "${Green}[INFO]${NC} $1"; }
ERROR() { echo -e "${Red}[ERROR]${NC} $1"; }
WARN() { echo -e "${Yellow}[WARN]${NC} $1"; }

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    ERROR "请使用root权限运行此脚本"
    exit 1
fi

# 创建必要的目录
INFO "创建必要的目录..."
mkdir -p /www/data/scripts || { ERROR "创建目录失败"; exit 1; }

# 复制更新脚本
INFO "安装更新脚本..."
cat > /www/data/scripts/update_quark_images.sh << 'EOF'
#!/bin/bash
# (这里是之前的update_quark_images.sh的内容，但去掉service创建部分)
# ... 复制之前脚本的内容到这里 ...
EOF

# 设置脚本权限
chmod +x /www/data/scripts/update_quark_images.sh

# 检查服务是否已存在
if systemctl is-active --quiet update-quark-images; then
    INFO "更新服务已存在并正在运行"
else
    INFO "创建系统服务..."
    # 创建service文件
    cat > /etc/systemd/system/update-quark-images.service << EOF
[Unit]
Description=Update Quark Docker Images Service
After=network.target docker.service
Wants=docker.service

[Service]
Type=simple
User=root
ExecStart=/www/data/scripts/update_quark_images.sh
Restart=on-failure
RestartSec=60

[Install]
WantedBy=multi-user.target
EOF

    # 重载systemd配置
    INFO "重载systemd配置..."
    systemctl daemon-reload

    # 启用并启动服务
    INFO "启用并启动服务..."
    systemctl enable update-quark-images
    systemctl start update-quark-images
fi

# 检查服务状态
if systemctl is-active --quiet update-quark-images; then
    INFO "服务已成功启动"
    INFO "可以使用以下命令查看服务状态："
    echo "systemctl status update-quark-images"
    echo "journalctl -u update-quark-images -f"
else
    ERROR "服务启动失败，请检查日志"
    systemctl status update-quark-images
    exit 1
fi 