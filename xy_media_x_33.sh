#!/bin/bash
# shellcheck shell=bash
# shellcheck disable=SC2086 # Allow word splitting
# shellcheck disable=SC2155 # Allow `local var=$(command)`
# shellcheck disable=SC2046 # Allow `rm $(command)` in specific safe cases
# shellcheck disable=SC2012 # ls usage for specific cases if any (prefer find)
# shellcheck disable=SC2129 # Redirecting to log file with tee

set -e # Exit immediately if a command exits with a non-zero status.
set -o pipefail # The return value of a pipeline is the status of the last command to exit with a non-zero status.

# --- Global Variables & Constants ---
PATH=${PATH}:/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin:~/bin:/opt/homebrew/bin
export PATH

Blue="\033[1;34m"
Green="\033[1;32m"
Red="\033[1;31m"
Yellow="\033[1;33m"
NC="\033[0m" # No Color

# Mode flags
AUTO_MODE=false
JELLYFIN_MODE=false

# Default values
source_dir=""
dist_dir=""
intermediate_dir="/mnt/disk1/emby_img" # Default for auto, prompt in manual
img_name=""
img_path=""
set_size_for_checks="" # Calculated in auto for disk space checks, user input in manual
xiaoya_addr=""
docker_addr=""
loop_device=""
LOG_FILE="" # Set after source_dir is known

# Files to monitor in auto mode
MONITORED_FILES=(
    "all.mp4" "115.mp4" "pikpak.mp4" "json.mp4"
    "短剧.mp4" "蓝光原盘.mp4" "config.mp4"
)
# Files used for initial image size calculation in auto mode
FILES_FOR_INITIAL_IMG_CALC=("all.mp4" "115.mp4" "json.mp4" "短剧.mp4" "蓝光原盘.mp4")

declare -A files_to_process_map # Associative array to store files that need processing and their remote sizes
files_to_process_list=() # Array of keys from files_to_process_map

declare -A FILE_TO_DIR_MAP=(
    ["all.mp4"]="📺画质演示测试（4K，8K，HDR，Dolby） 动漫 每日更新 测试 电影 电视剧 纪录片 纪录片（已刮削） 综艺 音乐"
    ["115.mp4"]="115"
    ["pikpak.mp4"]="PikPak"
    ["json.mp4"]="json"
    ["短剧.mp4"]="短剧"
    ["蓝光原盘.mp4"]="ISO"
    ["config.mp4"]="config"
)

# --- Logging Functions ---
log_message() {
    local type="$1"
    local message="$2"
    local timestamp
    timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    local formatted_message="$timestamp $type $message"

    # Always output to stderr
    echo -e "$formatted_message" >&2

    # Attempt to log to file if LOG_FILE is set
    if [ -n "$LOG_FILE" ]; then
        # Try to append to log file, but don't let script exit if the command fails
        # The subshell and 'if ! ...' construct handles the error gracefully without tripping 'set -e'
        if ! (echo -e "$formatted_message" >> "$LOG_FILE" 2>/dev/null); then
            # If appending to log file fails, print a warning to stderr
            # Using direct echo to avoid recursive call to log_message if it's the source of the problem
            local warn_timestamp=$(date "+%Y-%m-%d %H:%M:%S")
            echo -e "$warn_timestamp [${Yellow}WARN${NC}] Failed to write to log file $LOG_FILE. Message was: $type $message" >&2
        fi
    fi
}

INFO() { log_message "[${Green}INFO${NC}]" "$1"; }
ERROR() { log_message "[${Red}ERROR${NC}]" "$1"; }
WARN() { log_message "[${Yellow}WARN${NC}]" "$1"; }

log_and_exit() {
    ERROR "$1"
    # Call cleanup function if it's defined, otherwise just exit
    if command -v cleanup &> /dev/null; then
        cleanup
    fi
    exit 1
}

# --- Cleanup Function ---
cleanup() {
    INFO "Attempting cleanup..."
    # Stop docker container if it's the py-xy one
    if docker ps -a --format '{{.Names}}' | grep -q "^py-xy$"; then
        INFO "Stopping and removing docker container py-xy..."
        docker stop py-xy >/dev/null 2>&1 || WARN "Failed to stop py-xy container (may not be running)"
        docker rm py-xy >/dev/null 2>&1 || WARN "Failed to remove py-xy container (may not exist)"
    fi

    if [ -n "$dist_dir" ] && mount | grep -q " ${dist_dir}/emby-xy "; then
        INFO "Unmounting ${dist_dir}/emby-xy..."
        umount "${dist_dir}/emby-xy" || WARN "Failed to unmount ${dist_dir}/emby-xy"
    fi
    if [ -n "$loop_device" ] && losetup -a | grep -q "$loop_device"; then
        INFO "Detaching loop device ${loop_device}..."
        losetup -d "${loop_device}" || WARN "Failed to detach loop device ${loop_device}"
    fi
    INFO "Cleanup attempt finished."
}
trap cleanup EXIT SIGHUP SIGINT SIGTERM

# --- Utility Functions ---
check_command() {
    local cmd=$1
    if ! command -v "$cmd" &> /dev/null; then
        log_and_exit "错误: 命令 '$cmd' 未找到。请安装后重试。"
    fi
    INFO "Command '$cmd' found."
}

ensure_mount() {
    local mount_point="$1"
    local nfs_server_path="$2"
    local options="$3"

    INFO "Checking mount point: $mount_point for $nfs_server_path"
    if ! findmnt -M "$mount_point" > /dev/null; then
        INFO "$mount_point is not mounted. Attempting to mount."
        mkdir -p "$mount_point" || log_and_exit "Failed to create directory $mount_point"
        mount -t nfs -o "$options" "$nfs_server_path" "$mount_point" || log_and_exit "Failed to mount $nfs_server_path to $mount_point"
        INFO "$nfs_server_path mounted successfully to $mount_point."
    else
        INFO "$mount_point is already mounted."
    fi
}

get_remote_file_size_bytes() {
    local file_url="$1"
    local size_bytes
    size_bytes=$(curl -sL -D - --max-time 10 "$file_url" | grep -i "Content-Length" | awk '{print $2}' | tr -d '\r' | tail -n1)
    if [[ "$size_bytes" =~ ^[0-9]+$ ]]; then
        echo "$size_bytes"
    else
        echo "0" # Return 0 if size cannot be determined
    fi
}

get_local_file_size_bytes() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        du -b "$file_path" | cut -f1
    else
        echo "0" # Return 0 if file does not exist
    fi
}

check_disk_space() {
    local target_dir="$1"
    local required_gb="$2"
    local label="$3"

    INFO "Checking disk space for $label directory: $target_dir (requires ${required_gb}G)"
    if [ ! -d "$target_dir" ]; then
        # For target_dir, it might be created later. Check parent.
        # For source_dir, it must exist or be mountable.
        if [ "$label" == "Source" ]; then
             log_and_exit "$label directory $target_dir does not exist."
        fi
        INFO "$label directory $target_dir does not exist yet, will check parent."
        target_dir=$(dirname "$target_dir")
        if [ ! -d "$target_dir" ]; then
             mkdir -p "$target_dir" || log_and_exit "Failed to create parent for $label directory $target_dir."
        fi
    fi
    
    local available_gb
    available_gb=$(df -BG --output=avail "$target_dir" | tail -n1 | tr -d 'G')
    
    if [ "$available_gb" -lt "$required_gb" ]; then
        ERROR "$label directory $target_dir has insufficient space. Available: ${available_gb}G, Required: ${required_gb}G."
        return 1 # Failure
    fi
    INFO "$label directory $target_dir has sufficient space. Available: ${available_gb}G, Required: ${required_gb}G."
    return 0 # Success
}

_ensure_dist_dir_space_for_image() {
    local target_dist_dir="$1"
    local required_gb_for_img="$2"
    local image_full_path_to_create="$3" # This is the $img_path global variable

    INFO "Ensuring disk space for new image in $target_dist_dir. Required: ${required_gb_for_img}G for $image_full_path_to_create"

    # ALWAYS attempt to clean up the image_full_path_to_create if it exists
    if [ -f "$image_full_path_to_create" ]; then
        INFO "Target image file $image_full_path_to_create exists. Attempting to detach and delete it first."
        local old_loop_device_for_this_img
        old_loop_device_for_this_img=$(losetup -j "$image_full_path_to_create" | cut -d: -f1)

        if [ -n "$old_loop_device_for_this_img" ]; then
            INFO "Old image $image_full_path_to_create is associated with loop device $old_loop_device_for_this_img."
            if _robust_losetup_detach "$image_full_path_to_create" "loop_device"; then
                 INFO "Successfully detached old image $image_full_path_to_create."
            else
                 WARN "Failed to robustly detach old image $image_full_path_to_create. Deletion might fail or leave stale mounts."
            fi
        else
            INFO "Old image $image_full_path_to_create is not currently associated with a loop device via losetup -j."
        fi
        
        INFO "Deleting old image file: $image_full_path_to_create"
        rm -f "$image_full_path_to_create" || {
            WARN "Failed to delete old image file $image_full_path_to_create. Please check permissions or if it's in use."
        }
        INFO "Deletion attempt for $image_full_path_to_create finished."
    fi

    # Now, perform the first actual disk space check
    local space_check_status
    if check_disk_space "$target_dist_dir" "$required_gb_for_img" "Target Image"; then
        INFO "Sufficient space available in $target_dist_dir for the new image."
        return 0
    else
        space_check_status=1 # Mark as failure to proceed with further cleanup if needed
    fi

    # If space is still insufficient, try cleaning the specific old image
    WARN "Space check failed for $target_dist_dir. Required: ${required_gb_for_img}G. Attempting to clean up specific old image if applicable."
    
    local specific_old_image_to_check="${dist_dir}/emby-ailg-115.img"
    if [ "$image_full_path_to_create" != "$specific_old_image_to_check" ] && [ -f "$specific_old_image_to_check" ]; then
        INFO "Specific old image file $specific_old_image_to_check exists. Attempting to detach and delete it."
        if _robust_losetup_detach "$specific_old_image_to_check" "loop_device"; then
            INFO "Successfully detached specific old image $specific_old_image_to_check."
        else
            WARN "Failed to robustly detach specific old image $specific_old_image_to_check. Deletion might fail or leave stale mounts."
        fi
        INFO "Deleting specific old image file: $specific_old_image_to_check"
        rm -f "$specific_old_image_to_check" || {
            WARN "Failed to delete specific old image file $specific_old_image_to_check."
        }
        
        INFO "Re-checking disk space for $target_dist_dir after cleanup attempt for $specific_old_image_to_check."
        if check_disk_space "$target_dist_dir" "$required_gb_for_img" "Target Image (after cleanup for $specific_old_image_to_check)"; then
            INFO "Sufficient space available in $target_dist_dir after cleaning up $specific_old_image_to_check."
            return 0
        fi
        WARN "Space still insufficient after attempting to clean $specific_old_image_to_check."
    elif [ "$image_full_path_to_create" == "$specific_old_image_to_check" ]; then
        INFO "The image to create ($image_full_path_to_create) is the same as the specific old image. Its cleanup (if it existed) was handled when $image_full_path_to_create was processed."
        # If we are here, it means deleting it (if it existed as $image_full_path_to_create) wasn't enough.
    fi

    # Final decision
    # If we've reached this point and space_check_status was 1 (or a subsequent check failed), then log_and_exit.
    # The check_disk_space calls above would have returned 0 if successful, leading to an early exit from this function.
    log_and_exit "Insufficient space in $target_dist_dir for new image. Required: ${required_gb_for_img}G. Cleanup attempts failed to free enough space."
    return 1 # Should be caught by log_and_exit
}


# Functions to extract values from command outputs
extract_e2fsck_blocks() { # Arg: e2fsck output string
    echo "$1" | grep -oP '\d+(?=/\d+ blocks)' | tail -n1
}
extract_resize2fs_min_blocks() { # Arg: resize2fs output string
    echo "$1" | grep -oP 'minimum \((\K\d+)(?=\))'
}
extract_resize2fs_new_blocks() { # Arg: resize2fs output string
    echo "$1" | grep -oP '(now|already) \K\d+(?= \(4k\) blocks long.)' | tail -n1
}
extract_df_used_gb() { # Arg1: df -Th output, Arg2: loop_device path (e.g. /dev/loop10)
    # Ensure we match the exact loop device path, e.g., /dev/loop10 not /dev/loop100
    echo "$1" | grep -w "$2" | awk '{print $4}' | sed 's/G//'
}

_get_size_in_gb_from_du_output() {
    local du_output_str="$1"
    local dir_description="$2" # e.g., "PikPak" or "config"
    local size_val
    size_val=$(echo "$du_output_str" | awk '{print $1}' | sed 's/[GMKB]$//g; s/[^0-9.]//g') # Extract numeric part
    local size_unit
    size_unit=$(echo "$du_output_str" | awk '{print $1}' | sed 's/[0-9.]//g' | tr '[:lower:]' '[:upper:]') # Extract unit part and uppercase

    local size_gb_float=0
    if [[ -z "$size_val" ]]; then # Handle empty du_output_str
        size_val=0
    fi

    if [[ "$size_unit" == "G" ]]; then
        size_gb_float=$(awk -v val="$size_val" 'BEGIN{print val}')
    elif [[ "$size_unit" == "M" ]]; then
        size_gb_float=$(awk -v val="$size_val" 'BEGIN{print val / 1024}')
    elif [[ "$size_unit" == "K" ]]; then
        size_gb_float=$(awk -v val="$size_val" 'BEGIN{print val / (1024*1024)}')
    elif [[ "$size_unit" == "B" ]] || [[ -z "$size_unit" ]]; then # Assuming bytes if B or no unit
        size_gb_float=$(awk -v val="$size_val" 'BEGIN{print val / (1024*1024*1024)}')
    else # Unknown unit
        WARN "Unknown size unit '$size_unit' for $dir_description from '$du_output_str'. Assuming 0 GB."
        size_gb_float=0
    fi

    local final_gb
    final_gb=$(awk -v val="$size_gb_float" 'BEGIN{
        if (val == 0) print 0;
        else if (val > 0 && val < 1) print 1; /* If size is small but non-zero, count as 1GB for buffer */
        else print (val == int(val) ? val : int(val)+1) /* Ceiling for other non-zero values */
    }')
    
    # If the calculation resulted in 0, but the input string suggests a non-zero size (e.g. "500K")
    # and the directory actually has files, default to 1GB. This handles cases where du output is minimal.
    if [ "$final_gb" -eq 0 ] && [[ "$du_output_str" =~ [1-9] ]]; then
        local check_dir_path=""
        if [ "$dir_description" == "PikPak" ]; then
            check_dir_path="${intermediate_dir}/xiaoya/PikPak"
        elif [ "$dir_description" == "config" ]; then
            check_dir_path="${intermediate_dir}/config"
        fi
        if [ -n "$check_dir_path" ] && [ -d "$check_dir_path" ] && [ -n "$(ls -A "$check_dir_path" 2>/dev/null)" ]; then
            WARN "Parsed size for $dir_description ('$du_output_str') was 0GB but directory is not empty. Defaulting to 1GB."
            final_gb=1
        fi
    fi
    echo "$final_gb"
}

_robust_losetup_detach() {
    local target_img_path="$1"
    local specific_loop_device_context="$2" # The loop_device variable name from the calling context

    if [ -z "$target_img_path" ]; then
        WARN "_robust_losetup_detach: target_img_path is empty."
        # If specific_loop_device_context is provided, try to detach it directly
        if [ -n "$specific_loop_device_context" ] && losetup -a | grep -q "^${!specific_loop_device_context}:"; then
            INFO "Detaching specific loop device ${!specific_loop_device_context}..."
            losetup -d "${!specific_loop_device_context}"
            if ! losetup -a | grep -q "^${!specific_loop_device_context}:"; then
                INFO "Specific loop device ${!specific_loop_device_context} detached."
                # Clear the variable in the calling context
                eval "$specific_loop_device_context=''"
                return 0
            else
                WARN "Failed to detach specific loop device ${!specific_loop_device_context}."
                return 1
            fi
        fi
        return 1
    fi

    INFO "Attempting to robustly detach loop device associated with ${target_img_path} (context: $specific_loop_device_context=${!specific_loop_device_context})..."
    
    local mount_point_to_check="${dist_dir}/emby-xy"
    if mount | grep -qF "$mount_point_to_check"; then
        local mounted_loop_device_on_mountpoint
        mounted_loop_device_on_mountpoint=$(mount | grep "$mount_point_to_check" | awk '{print $1}')
        INFO "$mount_point_to_check is mounted on $mounted_loop_device_on_mountpoint. Attempting to unmount..."
        umount "$mount_point_to_check"
        if mount | grep -qF "$mount_point_to_check"; then
            log_and_exit "Failed to unmount $mount_point_to_check (on $mounted_loop_device_on_mountpoint) before detaching."
        else
            INFO "$mount_point_to_check unmounted successfully."
        fi
    fi

    local detach_attempts=0
    while true; do
        local current_loop_for_img
        current_loop_for_img=$(losetup -j "${target_img_path}" | cut -d: -f1)

        if [ -z "$current_loop_for_img" ]; then
            INFO "No loop device found associated with ${target_img_path} via losetup -j. Detach successful or not needed."
            if [ -n "$specific_loop_device_context" ] && [ -n "${!specific_loop_device_context}" ] && losetup -a | grep -q "^${!specific_loop_device_context}:"; then
                 WARN "Loop device ${!specific_loop_device_context} (from context) still appears associated with something after detach attempt for ${target_img_path}."
            elif [ -n "$specific_loop_device_context" ]; then
                 eval "$specific_loop_device_context=''" # Clear context variable as it's detached
            fi
            return 0
        fi

        INFO "Detaching ${current_loop_for_img} from ${target_img_path} (attempt $((detach_attempts + 1)))..."
        losetup -d "${current_loop_for_img}"
        sleep 1

        # ((detach_attempts++))
        let detach_attempts+=1 # Using let for potentially more robust arithmetic
        if [ $detach_attempts -ge 5 ]; then
            ERROR "Failed to detach loop device for ${target_img_path} after multiple attempts. Current association: $(losetup -j "${target_img_path}")"
            return 1
        fi
    done
}

# --- Argument Parsing ---
parse_args() {
    for arg in "$@"; do
        case $arg in
            --auto)
            AUTO_MODE=true
            INFO "Auto mode enabled by argument."
            shift
            ;;
            --jellyfin)
            JELLYFIN_MODE=true
            INFO "Jellyfin mode enabled by argument."
            shift
            ;;
            *)
            WARN "Unknown option: $arg"
            shift
            ;;
        esac
    done
}

# --- Auto Mode Specific Functions ---
auto_mode_setup_default_vars() {
    # This function now only sets up paths and names. Logging setup is deferred.
    # INFO "Setting up default variables for auto mode..."
    source_dir="/mnt/disks/192.168.9.233_inas1"
    dist_dir="/mnt/disk1/img"
    intermediate_dir="/mnt/disk1/emby_img" # Fixed as per request for auto mode

    # LOG_FILE related lines are moved to auto_mode_main after mount checks
    # LOG_FILE="$source_dir/temp/xy_img_auto.log"
    # mkdir -p "$(dirname "$LOG_FILE")" && touch "$LOG_FILE" || WARN "Could not create log directory/file at $LOG_FILE. Logging to stdout only."
    # INFO "Logging to $LOG_FILE"

    xiaoya_addr="http://192.168.9.233:5678"
    docker_addr="http://192.168.9.233:5678" # Same as xiaoya_addr as per request

    if [ "$JELLYFIN_MODE" = true ]; then
        img_name="jellyfin-ailg-115.img"
    else
        img_name="emby-ailg-115-4.9.img"
    fi
    img_path="${dist_dir}/${img_name}"
    INFO "Source dir: $source_dir"
    INFO "Dist dir: $dist_dir"
    INFO "Intermediate dir: $intermediate_dir"
    INFO "Xiaoya/Docker addr: $xiaoya_addr"
    INFO "Image name: $img_name"
    INFO "Image path: $img_path"
}

auto_mode_check_mounts() {
    INFO "Checking and ensuring NFS mounts for auto mode..."
    ensure_mount "$source_dir" "192.168.9.233:/volume5/m5" "nolock"
    ensure_mount "/mnt/disks/192.168.9.233_m4" "192.168.9.233:/volume4/m4/" "nolock"
}

auto_mode_check_115_download_validity() {
    INFO "Entering auto_mode_check_115_download_validity function."
    echo "DEBUG: docker_addr is '$docker_addr'" | tee -a "$LOG_FILE" # Ensure it goes to log too
    INFO "Checking 115 download validity..."
    echo "DEBUG: About to define download_url" | tee -a "$LOG_FILE"
    local download_url="$docker_addr/d/ailg_jf/115/gbox_intro.mp4"
    echo "DEBUG: download_url is '$download_url'" | tee -a "$LOG_FILE"
    local expected_size="17675105"
    echo "DEBUG: expected_size is '$expected_size'" | tee -a "$LOG_FILE"
    declare -i attempts=0
    declare -i max_attempts=3
    echo "DEBUG: attempts=$attempts, max_attempts=$max_attempts" | tee -a "$LOG_FILE"

    INFO "DEBUG: About to check INFO return code"
    local last_info_rc=$?
    echo "DEBUG: Return code of last INFO command: $last_info_rc" | tee -a "$LOG_FILE"

    echo "DEBUG: Testing while condition separately: [ $attempts -lt $max_attempts ]" | tee -a "$LOG_FILE"
    if [ "$attempts" -lt "$max_attempts" ]; then
        echo "DEBUG: Separate condition test is TRUE" | tee -a "$LOG_FILE"
    else
        echo "DEBUG: Separate condition test is FALSE" | tee -a "$LOG_FILE"
    fi
    local test_rc=$?
    echo "DEBUG: Return code of separate condition test: $test_rc" | tee -a "$LOG_FILE"


    INFO "DEBUG: Entering while loop condition check (actual loop)"
    while [ $attempts -lt $max_attempts ]; do
        echo "DEBUG: Inside while loop, current attempts before increment: $attempts" # Temporarily removed | tee -a "$LOG_FILE"
        
        local prev_attempts=$attempts
        set +e # Temporarily disable exit on error for this specific operation
        attempts=$((attempts + 1))
        local increment_rc=$?
        set -e # Re-enable exit on error immediately

        echo "DEBUG: attempts after increment (using \$((...)) ): $attempts, prev_attempts: $prev_attempts, increment_rc: $increment_rc" | tee -a "$LOG_FILE"
        if [ $increment_rc -ne 0 ]; then
            ERROR "Arithmetic increment 'attempts=\$((attempts + 1))' failed with rc: $increment_rc. attempts before: $prev_attempts, attempts after: $attempts"
            log_and_exit "Critical error during attempt increment using arithmetic expansion."
        elif [ "$attempts" -le "$prev_attempts" ]; then # Additional sanity check
            ERROR "Arithmetic increment did not increase 'attempts' value. Before: $prev_attempts, After: $attempts"
            log_and_exit "Critical error: attempt counter did not increment."
        fi

        INFO "Attempt $attempts of $max_attempts to check 115 download validity..."
        local remote_size
        remote_size=$(get_remote_file_size_bytes "$download_url")
        
        if [ "$remote_size" == "$expected_size" ]; then
            INFO "115 download is valid (gbox_intro.mp4 size: $remote_size)."
            return 0
        else
            WARN "115 download check failed on attempt $attempts. Remote size: '$remote_size', Expected: '$expected_size'."
            if [ $attempts -lt $max_attempts ]; then
                sleep 5 # Wait before retrying
            fi
        fi
    done
    log_and_exit "115 download validity check failed after $max_attempts attempts."
}

auto_mode_determine_files_to_process() {
    INFO "Determining files to process and populating remote file sizes map..."
    # files_to_process_map will now store sizes for ALL MONITORED_FILES
    # files_to_process_list will only store keys of files that ACTUALLY NEED PROCESSING
    
    # Clear the global map and list for this run, do NOT re-declare them as local
    files_to_process_map=()
    files_to_process_list=()

    for file_key in "${MONITORED_FILES[@]}"; do
        local remote_file_url="${docker_addr}/d/元数据/${file_key}"
        local local_file_full_path="$source_dir/temp/$file_key"

        INFO "Checking file: $file_key"
        local remote_s="0" # Default to 0
        local attempts=0
        local max_attempts=3
        local get_size_success=false
        while [ $attempts -lt $max_attempts ]; do
            let attempts+=1
            INFO "Attempt $attempts of $max_attempts to get remote size for $file_key from $remote_file_url"
            remote_s=$(get_remote_file_size_bytes "$remote_file_url")
            if [[ "$remote_s" =~ ^[0-9]+$ ]] && [ "$remote_s" -gt 10000000 ]; then
                INFO "Successfully got remote size for $file_key: $remote_s bytes."
                get_size_success=true
                break
            else
                WARN "Failed to get valid remote size (got '$remote_s') for $file_key on attempt $attempts. Retrying..."
                if [ $attempts -lt $max_attempts ]; then
                    sleep 5
                fi
            fi
        done

        if ! $get_size_success ; then
            WARN "Failed to get remote size for $file_key after $max_attempts attempts. Storing size as 0."
            exit 1
        fi
        
        # Store the fetched (or default 0) remote size in the map for ALL monitored files
        files_to_process_map["$file_key"]="$remote_s"
        INFO "Stored remote size for $file_key in map: ${files_to_process_map[$file_key]}"


        # Now, determine if this file actually needs processing for the files_to_process_list
        local local_s
        local_s=$(get_local_file_size_bytes "$local_file_full_path")
        INFO "Remote size for $file_key (for processing decision): $remote_s bytes. Local size: $local_s bytes."

        # If remote_s is 0 or empty after all attempts, it's problematic for comparison.
        # However, your original logic was to exit if remote_s is invalid for comparison.
        # Let's refine: if remote_s is truly 0 (meaning file is empty or non-existent remotely),
        # and local file exists, it's a mismatch. If remote_s is 0 and local doesn't exist, they "match" (both empty/gone).
        
        local needs_processing=false
        if [[ "$remote_s" =~ ^[1-9][0-9]*$ ]]; then # Valid, positive remote size
            if [ ! -f "$local_file_full_path" ] || [ "$local_s" != "$remote_s" ]; then
                needs_processing=true
                if [ ! -f "$local_file_full_path" ]; then
                    INFO "Local file $local_file_full_path not found. Adding $file_key to processing list."
                else
                    INFO "Size mismatch for $file_key (Local: $local_s, Remote: $remote_s). Adding to processing list."
                fi
            fi
        fi

        if $needs_processing ; then
            files_to_process_list+=("$file_key")
        else
            INFO "File $file_key does not require processing based on size comparison."
        fi
    done

    if [ ${#files_to_process_list[@]} -eq 0 ]; then
        INFO "All monitored files are up-to-date or do not require processing. Exiting."
        exit 0
    fi
    INFO "Files to process: ${files_to_process_list[*]}"
}

auto_mode_calculate_set_size_for_checks() {
    INFO "Calculating total remote size for disk space checks..."
    local total_size_gb_sum=0
    for file_key in "${files_to_process_list[@]}"; do
        local size_bytes="${files_to_process_map[$file_key]}"
        if [[ "$size_bytes" =~ ^[0-9]+$ ]] && [ "$size_bytes" -gt 0 ]; then
            # Convert bytes to GB, ceiling
            local size_gb_ceil
            size_gb_ceil=$(awk -v bytes="$size_bytes" 'BEGIN{ val = bytes / (1024*1024*1024); print (int(val) == val ? val : int(val)+1) }')
            total_size_gb_sum=$((total_size_gb_sum + size_gb_ceil))
        else
            WARN "Size for $file_key is 0 or invalid ('$size_bytes'), not adding to set_size_for_checks sum."
        fi
    done
    set_size_for_checks=$((total_size_gb_sum + 10)) # Add 10G buffer
    INFO "Calculated set_size_for_checks: ${set_size_for_checks}G"
}

auto_mode_calculate_initial_img_size_g() {
    INFO "Calculating initial image size (truncate size) using pre-fetched remote sizes..."
    declare -ig total_raw_size_bytes=0

    for file_key in "${FILES_FOR_INITIAL_IMG_CALC[@]}"; do
        local size_bytes_str="${files_to_process_map[$file_key]}" # Get from the map populated earlier
        
        if [[ -n "$size_bytes_str" ]] && [[ "$size_bytes_str" =~ ^[0-9]+$ ]]; then # Check if not empty and is a number (can be 0)
            if [[ "$size_bytes_str" -gt 0 ]]; then
                 INFO "Using pre-fetched remote size for $file_key (initial calc): $size_bytes_str bytes."
                 total_raw_size_bytes=$((total_raw_size_bytes + size_bytes_str))
            else
                 INFO "Pre-fetched remote size for $file_key (initial calc) is 0 bytes. Not adding to sum."
            fi
        else
            WARN "Could not find valid pre-fetched remote size for $file_key ('$size_bytes_str') in map during initial_img_size calculation. Assuming 0 for this file."
            # This case should ideally not happen if auto_mode_determine_files_to_process ran correctly
        fi
    done

    INFO "Total raw size for img calc (bytes): $total_raw_size_bytes"
    
    if [ "$total_raw_size_bytes" -eq 0 ]; then
        WARN "Total raw size for initial image calculation is 0. This might lead to a very small image. Defaulting to 10G for safety."
        # This could happen if all files in FILES_FOR_INITIAL_IMG_CALC had 0 size or failed to fetch.
        # Provide a minimum sensible default.
        exit 1
    fi

    local target_size_bytes_float
    target_size_bytes_float=$(awk "BEGIN {printf \"%.1f\", $total_raw_size_bytes * 1.5}")
    INFO "Target size (bytes * 1.5): $target_size_bytes_float"
    
    local initial_img_size_g_ceil
    initial_img_size_g_ceil=$(awk -v bytes="$target_size_bytes_float" 'BEGIN{ val = bytes / (1024*1024*1024); print (int(val) == val ? val : int(val)+1) }')
    INFO "Calculated initial_img_size_g (ceiling): ${initial_img_size_g_ceil}G"
    
    echo "$initial_img_size_g_ceil"
}

auto_mode_prepare_intermediate_dir_and_source_files() {
    INFO "Preparing intermediate directory and source files..."
    mkdir -p "${intermediate_dir}/xiaoya" || log_and_exit "Failed to create ${intermediate_dir}/xiaoya"
    chmod -R 777 "${intermediate_dir}/xiaoya" # As per request

    for file_to_download in "${files_to_process_list[@]}"; do
        # Delete old directories in intermediate_dir
        local dir_names_str="${FILE_TO_DIR_MAP[$file_to_download]}"
        if [ "$file_to_download" == "config.mp4" ]; then
            INFO "Deleting old config directory: ${intermediate_dir}/${dir_names_str}"
            rm -rf "${intermediate_dir:?}/${dir_names_str:?}" # Protect against empty vars
        else
            # Handle multiple dirs for all.mp4
            IFS=' ' read -r -a dir_array <<< "$dir_names_str"
            for dir_name_part in "${dir_array[@]}"; do
                if [ -n "$dir_name_part" ]; then # Ensure not empty
                    INFO "Deleting old data directory: ${intermediate_dir}/xiaoya/${dir_name_part}"
                    rm -rf "${intermediate_dir:?}/xiaoya/${dir_name_part:?}"
                fi
            done
        fi
        # Delete old downloaded file from source_dir
        INFO "Deleting old source file: ${source_dir}/${file_to_download}"
        rm -f "${source_dir:?}/temp/${file_to_download:?}" # Use -f to avoid error if not exists
    done
}

run_py_xy_optimization() {
    INFO "Running py-xy optimization script..."
    local db_path="${dist_dir}/temp/test/media_index.db"
    local log_path="${dist_dir}/temp/test/media.log"
    mkdir -p "$(dirname "$db_path")"

    if [ -f "$db_path" ]; then
      mv -f "$db_path" "${db_path%.db}.bak.db" || WARN "Failed to backup $db_path"
    fi
    if [ -f "$log_path" ]; then
      mv -f "$log_path" "${log_path%.log}.bak.log" || WARN "Failed to backup $log_path"
    fi
    
    # Ensure py-xy container is not already running from a previous failed attempt
    if docker ps -a --format '{{.Names}}' | grep -q "^py-xy$"; then
        INFO "Found existing py-xy container, removing it first."
        docker stop py-xy >/dev/null 2>&1
        docker rm py-xy >/dev/null 2>&1
    fi

    docker run -d --name py-xy --net host --privileged \
        -v "${dist_dir}/emby-xy/xiaoya:/ailg" \
        -v "${dist_dir}/temp:/temp" \
        ailg/py-xy:250528 \
        bash -c "cd /app && python optimize_media.py --root_dir /ailg --db_path /temp/test/media_index.db --log_file /temp/test/media.log"
    
    INFO "Waiting for py-xy container to complete (timeout 5 hour)..."
    local timeout_seconds=18000 # 5 hour
    local start_time
    start_time=$(date +%s)
    while docker ps --format '{{.Names}}' | grep -q "^py-xy$"; do
        current_time=$(date +%s)
        if [ $((current_time - start_time)) -gt $timeout_seconds ]; then
            docker logs py-xy
            log_and_exit "py-xy container timed out after 5 hour."
        fi
        sleep 10
        INFO "py-xy still running..."
    done

    # Check exit code of the container
    local exit_code
    exit_code=$(docker inspect py-xy --format='{{.State.ExitCode}}')
    docker logs py-xy # Log output regardless of status
    if [ "$exit_code" -ne 0 ]; then
        log_and_exit "py-xy container exited with error code $exit_code."
    fi
    INFO "py-xy optimization completed successfully. Exit code: $exit_code."
    docker rm py-xy >/dev/null 2>&1 # Clean up successful container
}


# --- Main Auto Mode Execution ---
auto_mode_main() {
    # Step 1: Setup basic paths and names (source_dir is needed for mount checks)
    auto_mode_setup_default_vars
    
    # Step 2: Ensure all required mounts are active. source_dir itself might be a mount.
    # INFO messages from ensure_mount will go to stdout if LOG_FILE isn't set yet.
    auto_mode_check_mounts

    # Step 3: Now that source_dir is confirmed (and mounted if it was an NFS share),
    # set up the LOG_FILE and start proper logging.
    LOG_FILE="$source_dir/temp/xy_img_auto.log"
    if ! mkdir -p "$(dirname "$LOG_FILE")"; then
        # If log directory creation fails, log to stderr and exit.
        echo "$(date "+%Y-%m-%d %H:%M:%S") [${Red}ERROR${NC}] Critical: Failed to create log directory $(dirname "$LOG_FILE"). Exiting." >&2
        exit 1
    fi
    if ! touch "$LOG_FILE"; then
        # If log file creation fails, log to stderr and exit.
        echo "$(date "+%Y-%m-%d %H:%M:%S") [${Red}ERROR${NC}] Critical: Failed to create log file $LOG_FILE. Check permissions and disk space on $source_dir. Exiting." >&2
        exit 1
    fi
    
    # First message to the actual log file
    INFO "Logging to $LOG_FILE"
    # Now log the previous steps' completion
    INFO "Default variables (paths/names) set up."
    INFO "NFS mounts checked and ensured."

    # Continue with the rest of auto mode logic
    auto_mode_check_115_download_validity
    auto_mode_determine_files_to_process # Populates files_to_process_list and files_to_process_map

    # Calculate set_size_for_checks (total size of files to download + buffer)
    auto_mode_calculate_set_size_for_checks # Sets global $set_size_for_checks
    
    # Check 1: Source directory space (for downloading raw .mp4 files)
    # This uses set_size_for_checks directly as the requirement.
    if ! check_disk_space "$source_dir/temp" "$set_size_for_checks" "Source (temp download)"; then
        log_and_exit "Source directory $source_dir/temp space check failed."
    fi

    # Check 2: Intermediate directory space (for extracting .mp4 files)
    local required_intermediate_space_float
    required_intermediate_space_float=$(awk "BEGIN {printf \"%.1f\", $set_size_for_checks * 1.5}")
    local required_intermediate_space_int
    required_intermediate_space_int=$(printf "%.0f" "$required_intermediate_space_float")
    if ! check_disk_space "$intermediate_dir" "$required_intermediate_space_int" "Intermediate extraction"; then
        log_and_exit "Intermediate directory $intermediate_dir space check failed."
    fi

    # Calculate initial_img_size_g (size for the new .img file based on specific content)
    local initial_img_size_g
    initial_img_size_g=$(auto_mode_calculate_initial_img_size_g) # This function now only echos the number
    INFO "Calculated initial .img file size will be: ${initial_img_size_g}G"

    # Check 3: Target/Dist directory space (for the new .img file itself)
    local required_dist_for_img_gb=$((initial_img_size_g + 10)) # initial_img_size_g + 10G buffer
    _ensure_dist_dir_space_for_image "$dist_dir" "$required_dist_for_img_gb" "$img_path"
    # _ensure_dist_dir_space_for_image will log_and_exit if space cannot be made.
    
    # If we reach here, $img_path (if it existed) has been deleted by _ensure_dist_dir_space_for_image if space was initially insufficient.
    # Or, it never existed and there was enough space.
    # It's safe to try creating it now.
    
    INFO "Creating base image file: $img_path with size ${initial_img_size_g}G"
    truncate -s "${initial_img_size_g}G" "$img_path" || log_and_exit "Failed to truncate image file $img_path"

    INFO "Initializing image file system..."
    loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device"
    INFO "Using loop device: $loop_device"
    losetup "${loop_device}" "$img_path" || log_and_exit "Failed to losetup $img_path to $loop_device"
    
    INFO "Formatting ${loop_device} with ext4..."
    mkfs.ext4 -F -b 4096 -O ^metadata_csum,^64bit,^bigalloc -O has_journal,ext_attr,resize_inode,dir_index,filetype,extent,flex_bg,sparse_super,large_file,huge_file,inline_data,uninit_bg,fast_commit "${loop_device}" || log_and_exit "mkfs.ext4 failed"
    tune2fs -m 0 "${loop_device}" || log_and_exit "tune2fs failed"

    mkdir -p "${dist_dir}/emby-xy" || log_and_exit "Failed to create mount point ${dist_dir}/emby-xy"
    mount -o noatime,nodiratime,data=writeback "${loop_device}" "${dist_dir}/emby-xy" || log_and_exit "Failed to mount ${loop_device} to ${dist_dir}/emby-xy"
    
    mkdir -p "${dist_dir}/emby-xy/xiaoya" || log_and_exit "Failed to create ${dist_dir}/emby-xy/xiaoya"
    chmod -R 777 "${dist_dir}/emby-xy/xiaoya" || log_and_exit "Failed to chmod ${dist_dir}/emby-xy/xiaoya"

    auto_mode_prepare_intermediate_dir_and_source_files

    INFO "Starting download and extraction process..."
    for file_key_to_download in "${files_to_process_list[@]}"; do
        INFO "Processing file for download: $file_key_to_download"
        local download_file_name="$file_key_to_download"
        # Handle config.mp4 vs config.new.mp4 for download URL
        local actual_download_name_on_server="$file_key_to_download"
        # if [ "$file_key_to_download" == "config.mp4" ]; then
        #     actual_download_name_on_server="config.new.mp4" # Original script downloads config.new.mp4
        #     INFO "Will download 'config.new.mp4' as '$file_key_to_download'"
        # fi

        docker run --rm --net=host \
            -v "${source_dir}:/source" \
            -v "${intermediate_dir}:/dist" \
            ailg/ggbond:latest \
            bash -c "cd /source/temp && \
                    echo 'Downloading ${actual_download_name_on_server} to ${file_key_to_download}...' && \
                    aria2c -o \"${file_key_to_download}\" --auto-file-renaming=false --allow-overwrite=true -c -x6 \"${xiaoya_addr}/d/元数据/${actual_download_name_on_server}\" && \
                    echo 'Extracting ${file_key_to_download}...' && \
                    if [ \"$file_key_to_download\" = \"config.mp4\" ]; then \
                        7z x -aoa -bb1 -mmt=16 \"${file_key_to_download}\" -o\"/dist/\" ; \
                    else \
                        7z x -aoa -bb1 -mmt=16 \"${file_key_to_download}\" -o\"/dist/xiaoya\" ; \
                    fi"
        
        if [ $? -eq 0 ]; then
            INFO "√ $file_key_to_download processed successfully."
        else
            log_and_exit "× $file_key_to_download processing failed."
        fi
    done
    INFO "All selected files downloaded and extracted to intermediate directory."

    INFO "Rsyncing files (excluding PikPak) from intermediate to image mount..."
    rsync -av --progress --exclude="PikPak" "${intermediate_dir}/xiaoya/" "${dist_dir}/emby-xy/xiaoya/" || log_and_exit "Initial rsync failed"
    
    # --- Call common processing function ---
    # Ensure ver and base_img_filename are set for the common function
    ver=$(date +%y%m%d)
    base_img_filename=$(basename "$img_path")
    _perform_image_processing_and_finalization
    # --- End of common processing call ---
    
    INFO "Auto mode finished successfully."
}


# --- Common Image Processing and Finalization Function ---
_perform_image_processing_and_finalization() {
    INFO "Starting common image processing and finalization steps..."
    # Expects global vars: loop_device, img_path, dist_dir, intermediate_dir, JELLYFIN_MODE, ver, base_img_filename to be set correctly by caller.

    INFO "First filesystem check and resize sequence..."
    local e2fsck_output
    e2fsck_output=$(e2fsck -f -y "${loop_device}" 2>&1) || log_and_exit "e2fsck (1) failed. Output: $e2fsck_output"
    INFO "e2fsck (1) output: $e2fsck_output"
    local mini_block
    mini_block=$(extract_e2fsck_blocks "$e2fsck_output")
    if ! [[ "$mini_block" =~ ^[0-9]+$ ]] || [ "$mini_block" -eq 0 ]; then log_and_exit "Failed to extract mini_block from e2fsck output."; fi
    INFO "Extracted mini_block (count of 4K blocks): $mini_block"

    local mini_K_for_resize=$((mini_block * 4)) # Convert 4K block count to Kilobytes for resize2fs K suffix
    INFO "Calculated mini_K_for_resize: ${mini_K_for_resize}K"
    local resize_output1
    resize_output1=$(resize2fs "${loop_device}" "${mini_K_for_resize}K" 2>&1) || WARN "Initial resize2fs to mini_K_for_resize failed or warned (often expected). Output: $resize_output1"
    INFO "resize2fs (to mini_K_for_resize) output: $resize_output1"
    
    local m_K_from_output_4k_blocks
    m_K_from_output_4k_blocks=$(extract_resize2fs_min_blocks "$resize_output1") # Extracts XXXXX from "minimum (XXXXX)" which is in 4K blocks
    if ! [[ "$m_K_from_output_4k_blocks" =~ ^[0-9]+$ ]] || [ "$m_K_from_output_4k_blocks" -eq 0 ]; then log_and_exit "Failed to extract m_K_from_output_4k_blocks. Output: $resize_output1"; fi
    
    local m_K_for_actual_resize=$((m_K_from_output_4k_blocks * 4)) # Convert 4K block count to Kilobytes for resize2fs K suffix
    INFO "Extracted m_K_from_output_4k_blocks: $m_K_from_output_4k_blocks. Using ${m_K_for_actual_resize}K for actual resize."

    local resize_output2
    resize_output2=$(resize2fs "${loop_device}" "${m_K_for_actual_resize}K" 2>&1) || log_and_exit "resize2fs to m_K_for_actual_resize failed. Output: $resize_output2"
    INFO "resize2fs (to m_K_for_actual_resize) output: $resize_output2"
    local modi_block1
    modi_block1=$(extract_resize2fs_new_blocks "$resize_output2")
    if ! [[ "$modi_block1" =~ ^[0-9]+$ ]] || [ "$modi_block1" -eq 0 ]; then log_and_exit "Failed to extract modi_block1. Output: $resize_output2"; fi
    INFO "Extracted modi_block1 (final 4K blocks): $modi_block1"

    local modi_bytes1=$((modi_block1 * 4096))
    INFO "Calculated modi_bytes1 for truncate: $modi_bytes1"
    
    _robust_losetup_detach "$img_path" "loop_device" || log_and_exit "Failed to detach loop_device for first truncate"
    
    truncate -s "${modi_bytes1}" "$img_path" || log_and_exit "First truncate to modi_bytes1 failed"
    
    # loop_device global variable should be updated by _robust_losetup_detach if it was cleared
    # or we need to re-acquire it if it was fully detached from any specific device.
    # For safety, always re-acquire if it's empty.
    if [ -z "$loop_device" ]; then
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device for re-setup"
    fi
    losetup "${loop_device}" "$img_path" || log_and_exit "Failed to re-losetup $img_path to $loop_device after first truncate"
    INFO "Re-attached $img_path to $loop_device"
    e2fsck -f -y "${loop_device}" || log_and_exit "e2fsck after first truncate failed"
    mount -o noatime,nodiratime,data=writeback "${loop_device}" "${dist_dir}/emby-xy" || log_and_exit "Failed to re-mount after first truncate"
    INFO "First resize sequence completed."

    run_py_xy_optimization # First optimization pass

    INFO "Second resize sequence (PikPak preparation)..."
    local df_output1
    df_output1=$(df -Th 2>&1) || log_and_exit "Failed to get df output (1)"
    local img_used1_gb
    img_used1_gb=$(extract_df_used_gb "$df_output1" "$loop_device")
    if ! [[ "$img_used1_gb" =~ ^[0-9.]+$ ]]; then log_and_exit "Failed to extract img_used1_gb (numeric). df output: $df_output1"; fi
    img_used1_gb=$(printf "%.0f" "$img_used1_gb")
    INFO "Image used space (img_used1_gb): ${img_used1_gb}G"

    local pp_du_str
    pp_du_str=$(du -sh "${intermediate_dir}/xiaoya/PikPak" 2>/dev/null | awk '{print $1}')
    local pp_size_gb
    pp_size_gb=$(_get_size_in_gb_from_du_output "$pp_du_str" "PikPak")
    INFO "PikPak directory size (pp_size_gb): ${pp_size_gb}G (from '$pp_du_str')"
    
    local modi_before_pp_k=$(((img_used1_gb + pp_size_gb + 1) * 1024)) # Total Megabytes, used with K suffix for resize2fs (so it's interpreted as KB)
    INFO "Calculated modi_before_pp_k (value for K suffix): ${modi_before_pp_k}"

    _robust_losetup_detach "$img_path" "loop_device" || log_and_exit "Failed to detach loop_device for PikPak prep"
    
    if [ -z "$loop_device" ]; then # Re-acquire if detached fully
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device for PikPak e2fsck/resize"
    fi
    losetup "${loop_device}" "$img_path" || log_and_exit "Failed to re-losetup $img_path to $loop_device for PikPak e2fsck/resize"
    INFO "Re-attached $img_path to $loop_device for PikPak e2fsck/resize"
    e2fsck -f -y "${loop_device}" || log_and_exit "e2fsck (on re-attached ${loop_device}) before PikPak prep resize failed"

    local resize_output3
    resize_output3=$(resize2fs "${loop_device}" "${modi_before_pp_k}K" 2>&1) || log_and_exit "resize2fs for PikPak prep failed. Output: $resize_output3"
    INFO "resize2fs (PikPak prep) output: $resize_output3"
    local modi_block2
    modi_block2=$(extract_resize2fs_new_blocks "$resize_output3")
    if ! [[ "$modi_block2" =~ ^[0-9]+$ ]] || [ "$modi_block2" -eq 0 ]; then log_and_exit "Failed to extract modi_block2. Output: $resize_output3"; fi
    INFO "Extracted modi_block2 (4K blocks): $modi_block2"

    local modi_bytes2_calc=$((modi_block2 * 4096))
    local modi_before_pp_bytes_target=$((modi_before_pp_k * 1024))
    if [ "$modi_bytes2_calc" -ne "$modi_before_pp_bytes_target" ]; then
        WARN "Potential discrepancy in PikPak prep resize: actual_bytes ($modi_bytes2_calc) != target_bytes ($modi_before_pp_bytes_target). Proceeding with actual_bytes."
    fi
    INFO "Calculated modi_bytes2 for truncate (from modi_block2): $modi_bytes2_calc"
    _robust_losetup_detach "$img_path" "loop_device" || log_and_exit "Failed to detach loop_device for PikPak prep truncate"
    
    truncate -s "${modi_bytes2_calc}" "$img_path" || log_and_exit "PikPak prep truncate failed"
    
    if [ -z "$loop_device" ]; then
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device after PikPak truncate"
    fi
    losetup "${loop_device}" "$img_path" || log_and_exit "Failed to re-losetup $img_path to $loop_device after PikPak prep truncate"
    INFO "Re-attached $img_path to $loop_device after PikPak truncate"
    e2fsck -f -y "${loop_device}" || log_and_exit "e2fsck after PikPak prep truncate failed"
    INFO "Second resize sequence (PikPak preparation) completed."

    INFO "Copying image for _nopp_noconfig version..."
    _robust_losetup_detach "$img_path" "loop_device" || WARN "Failed to detach loop_device before copying _nopp_noconfig (might be already detached or error is acceptable here)"
    
    local nopp_noconfig_dir="/mnt/disks/192.168.9.233_m4/my_img/emby/ver_${ver}"
    mkdir -p "$nopp_noconfig_dir" || log_and_exit "Failed to create directory $nopp_noconfig_dir"
    
    local nopp_noconfig_img_path="${nopp_noconfig_dir}/${base_img_filename%.img}_nopp_noconfig.img"
    cp -f "$img_path" "$nopp_noconfig_img_path" || log_and_exit "Failed to copy to $nopp_noconfig_img_path"
    INFO "_nopp_noconfig image copied to $nopp_noconfig_img_path"

    INFO "Rsyncing PikPak content..."
    if [ -z "$loop_device" ]; then
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device for PikPak rsync"
    fi
    losetup "${loop_device}" "$img_path" || log_and_exit "Failed to losetup $img_path to $loop_device for PikPak rsync"
    INFO "Attached $img_path to $loop_device for PikPak rsync"
    mount -o noatime,nodiratime,data=writeback "${loop_device}" "${dist_dir}/emby-xy" || log_and_exit "Failed to mount for PikPak rsync"
    mkdir -p "${dist_dir}/emby-xy/xiaoya/PikPak" || log_and_exit "Failed to create PikPak dir on image"
    rsync -av --progress "${intermediate_dir}/xiaoya/PikPak/" "${dist_dir}/emby-xy/xiaoya/PikPak/" || log_and_exit "PikPak rsync failed"
    
    run_py_xy_optimization # Second optimization pass (after PikPak)

    INFO "Third resize sequence (config preparation)..."
    local df_output2
    df_output2=$(df -Th 2>&1) || log_and_exit "Failed to get df output (2)"
    local img_used2_gb
    img_used2_gb=$(extract_df_used_gb "$df_output2" "$loop_device")
    if ! [[ "$img_used2_gb" =~ ^[0-9.]+$ ]]; then log_and_exit "Failed to extract img_used2_gb (numeric). df output: $df_output2"; fi
    img_used2_gb=$(printf "%.0f" "$img_used2_gb")
    INFO "Image used space (img_used2_gb): ${img_used2_gb}G"

    local config_du_str
    config_du_str=$(du -sh "${intermediate_dir}/config" 2>/dev/null | awk '{print $1}')
    local config_size_gb
    config_size_gb=$(_get_size_in_gb_from_du_output "$config_du_str" "config")
    INFO "Config directory size (config_size_gb): ${config_size_gb}G (from '$config_du_str')"

    local modi_before_config_k=$(((img_used2_gb + config_size_gb + 1) * 1024)) # Megabytes, for K suffix
    INFO "Calculated modi_before_config_k (value for K suffix): ${modi_before_config_k}"

    _robust_losetup_detach "$img_path" "loop_device" || log_and_exit "Failed to detach loop_device for config prep"
    if [ -z "$loop_device" ]; then
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device for config e2fsck/resize"
    fi
    losetup "${loop_device}" "$img_path" || log_and_exit "Failed to re-losetup $img_path to $loop_device for config e2fsck/resize"
    INFO "Re-attached $img_path to $loop_device for config e2fsck/resize"
    e2fsck -f -y "${loop_device}" || log_and_exit "e2fsck before config prep resize failed"
    
    local resize_output4
    resize_output4=$(resize2fs "${loop_device}" "${modi_before_config_k}K" 2>&1) || log_and_exit "resize2fs for config prep failed. Output: $resize_output4"
    INFO "resize2fs (config prep) output: $resize_output4"
    local modi_block3
    modi_block3=$(extract_resize2fs_new_blocks "$resize_output4")
    if ! [[ "$modi_block3" =~ ^[0-9]+$ ]] || [ "$modi_block3" -eq 0 ]; then log_and_exit "Failed to extract modi_block3. Output: $resize_output4"; fi
    INFO "Extracted modi_block3 (4K blocks): $modi_block3"

    local modi_bytes3_calc=$((modi_block3 * 4096))
    local modi_before_config_bytes_target=$((modi_before_config_k * 1024))
    if [ "$modi_bytes3_calc" -ne "$modi_before_config_bytes_target" ]; then
        WARN "Potential discrepancy in config prep resize: actual_bytes ($modi_bytes3_calc) != target_bytes ($modi_before_config_bytes_target). Proceeding."
    fi
    INFO "Calculated modi_bytes3 for truncate (from modi_block3): $modi_bytes3_calc"
    _robust_losetup_detach "$img_path" "loop_device" || log_and_exit "Failed to detach loop_device for config prep truncate"
    
    truncate -s "${modi_bytes3_calc}" "$img_path" || log_and_exit "Config prep truncate failed"
    
    if [ -z "$loop_device" ]; then
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device after config truncate"
    fi
    losetup "${loop_device}" "$img_path" || log_and_exit "Failed to re-losetup $img_path to $loop_device after config prep truncate"
    INFO "Re-attached $img_path to $loop_device after config truncate"
    e2fsck -f -y "${loop_device}" || log_and_exit "e2fsck after config prep truncate failed"
    INFO "Third resize sequence (config preparation) completed."

    INFO "Rsyncing config content..."
    mount -o noatime,nodiratime,data=writeback "${loop_device}" "${dist_dir}/emby-xy" || log_and_exit "Failed to mount for config rsync"
    mkdir -p "${dist_dir}/emby-xy/config" || log_and_exit "Failed to create config dir on image"
    rsync -av --progress "${intermediate_dir}/config/" "${dist_dir}/emby-xy/config/" || log_and_exit "Config rsync failed"

    INFO "Performing final strm file modifications..."
    find "${dist_dir}/emby-xy/xiaoya/PikPak" -type f -name "*.strm" -exec sed -i 's/🕸️/🥒/g' {} \; || WARN "sed command on strm files failed or found no files."

    _robust_losetup_detach "$img_path" "loop_device" || log_and_exit "Failed to detach loop_device before final cat for main image"
    
    local final_mp4_path="${nopp_noconfig_dir}/${base_img_filename%.img}.mp4"
    INFO "Creating final MP4 for main image: $final_mp4_path"
    cat "/mnt/disks/192.168.9.233_m4/my_img/test.mp4" "$img_path" > "$final_mp4_path" || log_and_exit "Final cat operation for main image failed"
    INFO "Main image processing complete. Final MP4 at $final_mp4_path"

    # Lite Image Processing
    INFO "Starting Lite Image processing..."
    rm -f "$img_path"
    local lite_img_name_base="${base_img_filename%-115-4.9.img}"
    if [ "$JELLYFIN_MODE" = true ]; then
        lite_img_name_base="${base_img_filename%-115.img}"
    fi
    local lite_img_path="${dist_dir}/${lite_img_name_base}-lite-115-4.9.img"
     if [ "$JELLYFIN_MODE" = true ]; then
        lite_img_path="${dist_dir}/${lite_img_name_base}-lite-115.img"
    fi

    INFO "Copying _nopp_noconfig image to lite image path: $lite_img_path from $nopp_noconfig_img_path"
    cp -f "$nopp_noconfig_img_path" "$lite_img_path" || log_and_exit "Failed to copy for lite image base"

    local lite_modi_before_config_k=$(((img_used1_gb + config_size_gb + 1) * 1024)) # Megabytes, for K suffix
    INFO "Calculated lite_modi_before_config_k (value for K suffix): ${lite_modi_before_config_k}"

    if [ -z "$loop_device" ]; then # Ensure loop_device is available
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device for lite image setup"
    fi
    losetup "${loop_device}" "$lite_img_path" || log_and_exit "Failed to losetup $lite_img_path to $loop_device"
    INFO "Attached $lite_img_path to $loop_device"
    e2fsck -f -y "${loop_device}" || log_and_exit "e2fsck for lite image failed"
    
    local resize_output_lite
    resize_output_lite=$(resize2fs "${loop_device}" "${lite_modi_before_config_k}K" 2>&1) || log_and_exit "resize2fs for lite image failed. Output: $resize_output_lite"
    INFO "resize2fs (lite image) output: $resize_output_lite"
    local modi_block4
    modi_block4=$(extract_resize2fs_new_blocks "$resize_output_lite")
    if ! [[ "$modi_block4" =~ ^[0-9]+$ ]] || [ "$modi_block4" -eq 0 ]; then log_and_exit "Failed to extract modi_block4 for lite image. Output: $resize_output_lite"; fi
    INFO "Extracted modi_block4 for lite image (4K blocks): $modi_block4"

    local modi_bytes4_calc=$((modi_block4 * 4096))
    local lite_modi_before_config_bytes_target=$((lite_modi_before_config_k * 1024))
     if [ "$modi_bytes4_calc" -ne "$lite_modi_before_config_bytes_target" ]; then
        WARN "Potential discrepancy in lite image resize: actual_bytes ($modi_bytes4_calc) != target_bytes ($lite_modi_before_config_bytes_target). Proceeding."
    fi
    INFO "Calculated modi_bytes4 for lite image truncate: $modi_bytes4_calc"
    _robust_losetup_detach "$lite_img_path" "loop_device" || log_and_exit "Failed to detach for lite image truncate"
    
    truncate -s "${modi_bytes4_calc}" "$lite_img_path" || log_and_exit "Lite image truncate failed"
    
    if [ -z "$loop_device" ]; then
        loop_device=$(losetup -f) || log_and_exit "Failed to get free loop device after lite truncate"
    fi
    losetup "${loop_device}" "$lite_img_path" || log_and_exit "Failed to re-losetup $lite_img_path to $loop_device after lite truncate"
    INFO "Re-attached $lite_img_path to $loop_device after lite truncate"
    e2fsck -f -y "${loop_device}" || log_and_exit "e2fsck for lite image after truncate failed"
    INFO "Lite image resize sequence completed."

    INFO "Rsyncing config to lite image..."
    mount -o noatime,nodiratime,data=writeback "${loop_device}" "${dist_dir}/emby-xy" || log_and_exit "Failed to mount lite image for config rsync"
    mkdir -p "${dist_dir}/emby-xy/config"
    rsync -av --progress "${intermediate_dir}/config/" "${dist_dir}/emby-xy/config/" || log_and_exit "Lite image config rsync failed"

    _robust_losetup_detach "$lite_img_path" "loop_device" || log_and_exit "Failed to detach lite image loop_device before final cat"

    local lite_base_filename
    lite_base_filename=$(basename "$lite_img_path")
    local final_lite_mp4_path="${nopp_noconfig_dir}/${lite_base_filename%.img}.mp4"
    INFO "Creating final MP4 for lite image: $final_lite_mp4_path"
    cat "/mnt/disks/192.168.9.233_m4/my_img/test.mp4" "$lite_img_path" > "$final_lite_mp4_path" || log_and_exit "Final cat operation for lite image failed"
    INFO "Lite image processing complete. Final MP4 at $final_lite_mp4_path"
}


# --- Manual Mode Main Function ---
manual_mode_main() {
    INFO "Starting manual mode setup..."

    # Get source_dir first for logging
    read -erp "请输入源目录路径 (default: /mnt/disks/192.168.9.233_inas1): " source_dir_manual
    source_dir="${source_dir_manual:-/mnt/disks/192.168.9.233_inas1}"
    if [ ! -d "$source_dir" ]; then
        read -erp "源目录 $source_dir 不存在，是否创建? (Y/n): " create_src_dir
        if [[ "$create_src_dir" =~ ^[Yy]$ ]] || [ -z "$create_src_dir" ]; then
            mkdir -p "$source_dir" || log_and_exit "创建源目录失败: $source_dir"
        else
            log_and_exit "源目录未创建，脚本中止."
        fi
    fi
    LOG_FILE="$source_dir/temp/xy_img_auto.log" # Use same log file name convention
    mkdir -p "$(dirname "$LOG_FILE")" && touch "$LOG_FILE" || WARN "Could not create log directory/file at $LOG_FILE."
    INFO "Logging to $LOG_FILE"
    INFO "Manual Mode: Source directory set to $source_dir"

    local set_size_manual
    read -erp "请输入预期空间大小(GB，建议为实际文件大小的150%): " set_size_manual
    while ! [[ "$set_size_manual" =~ ^[0-9]+$ ]] || [ "$set_size_manual" -le 0 ]; do
        ERROR "请输入有效的正整数作为空间大小."
        read -erp "请输入预期空间大小(GB): " set_size_manual
    done
    INFO "Manual Mode: Expected image size set to ${set_size_manual}G"

    read -erp "请输入目标目录路径 (default: /mnt/disk1/img): " dist_dir_manual
    dist_dir="${dist_dir_manual:-/mnt/disk1/img}"
    if [ ! -d "$dist_dir" ]; then
        read -erp "目标目录 $dist_dir 不存在，是否创建? (Y/n): " create_dist_dir
        if [[ "$create_dist_dir" =~ ^[Yy]$ ]] || [ -z "$create_dist_dir" ]; then
            mkdir -p "$dist_dir" || log_and_exit "创建目标目录失败: $dist_dir"
        else
            log_and_exit "目标目录未创建，脚本中止."
        fi
    fi
    INFO "Manual Mode: Target directory set to $dist_dir"
    
    # Prompt for intermediate directory in manual mode
    read -erp "请输入下载和解压的中间目录 (default: /mnt/disk1/emby_img): " intermediate_dir_manual
    intermediate_dir="${intermediate_dir_manual:-/mnt/disk1/emby_img}"
    mkdir -p "${intermediate_dir}/xiaoya" || log_and_exit "Failed to create manual intermediate dir ${intermediate_dir}/xiaoya"
    INFO "Manual Mode: Intermediate directory for downloads: $intermediate_dir"


    # Disk space checks for manual mode
    # Check 1: Source directory (for downloads to source_dir/temp)
    # For manual mode, set_size_manual is the user's *image* size expectation.
    # We need to estimate download size. Let's assume downloads could be up to set_size_manual.
    if ! check_disk_space "$source_dir/temp" "$set_size_manual" "Source (temp download)"; then
        log_and_exit "Manual mode: Source directory $source_dir/temp space check failed."
    fi

    # Check 2: Intermediate directory (for extractions)
    # Requirement: 1.5 * estimated download size (which we'll again peg to set_size_manual for simplicity here)
    local required_intermediate_manual_float
    required_intermediate_manual_float=$(awk "BEGIN {printf \"%.1f\", $set_size_manual * 1.5}")
    local required_intermediate_manual_int
    required_intermediate_manual_int=$(printf "%.0f" "$required_intermediate_manual_float")
    if ! check_disk_space "$intermediate_dir" "$required_intermediate_manual_int" "Intermediate extraction"; then
        log_and_exit "Manual mode: Intermediate directory $intermediate_dir space check failed."
    fi
    
    # Select image name (this determines img_path)
    local img_options_manual=(
        "emby-ailg-115-4.9.img" "emby-ailg-lite-115-4.9.img"
        "jellyfin-ailg-115.img" "jellyfin-ailg-lite-115.img"
    )
    echo -e "请选择镜像名："
    for index in "${!img_options_manual[@]}"; do
        printf "[ %-1d ] %s\n" $((index + 1)) "${img_options_manual[$index]}"
    done
    printf "[ 0 ] 自定义镜像名\n"
    local img_select_manual
    while :; do
        read -erp "请输入序号: " img_select_manual
        if [[ "$img_select_manual" =~ ^[0-9]+$ ]] && [ "$img_select_manual" -ge 0 ] && [ "$img_select_manual" -le ${#img_options_manual[@]} ]; then
            if [ "$img_select_manual" -eq 0 ]; then
                read -erp "请输入自定义镜像名: " img_name
            else
                img_name="${img_options_manual[$((img_select_manual - 1))]}"
            fi
            if [ -z "$img_name" ]; then ERROR "镜像名不能为空"; else break; fi
        else
            ERROR "无效的序号."
        fi
    done
    img_path="${dist_dir}/${img_name}"
    INFO "Manual Mode: Image path set to $img_path"

    # Get Xiaoya address (similar to original)
    local docker0_ip
    docker0_ip=$(ip address | grep inet | grep -v 172.17 | grep -v 169. | grep -v 127.0.0.1 | grep -v inet6 | awk '{print $2}' | sed 's/addr://' | head -n1 | cut -f1 -d"/")
    local default_xiaoya_addr_manual="http://${docker0_ip}:5678"
    read -erp "请输入小雅alist地址 (default: $default_xiaoya_addr_manual): " xiaoya_addr_manual
    xiaoya_addr="${xiaoya_addr_manual:-$default_xiaoya_addr_manual}"
    if ! curl -sIL "${xiaoya_addr}/d/README.md" --max-time 5 | grep -v 302 | grep -q "x-oss-"; then # Check validity
        log_and_exit "无法连接到小雅alist: $xiaoya_addr"
    fi
    INFO "Manual Mode: Xiaoya address set to $xiaoya_addr"
    docker_addr="$xiaoya_addr"

    if [[ "$img_name" == *"jellyfin"* ]]; then
        JELLYFIN_MODE=true
        INFO "Jellyfin mode inferred from image name in manual mode: $img_name"
    else
        JELLYFIN_MODE=false
        INFO "Emby mode inferred from image name in manual mode: $img_name"
    fi

    # Create and mount image (using user's set_size_manual)
    # Check 3: Target/Dist directory space for the new .img file
    # In manual mode, initial_img_size_g is effectively set_size_manual from user input.
    local required_dist_manual_gb=$((set_size_manual + 10)) # set_size_manual + 10G buffer
    _ensure_dist_dir_space_for_image "$dist_dir" "$required_dist_manual_gb" "$img_path"
    # _ensure_dist_dir_space_for_image will log_and_exit if space cannot be made.

    INFO "Manual Mode: Creating base image file: $img_path with size ${set_size_manual}G"
    truncate -s "${set_size_manual}G" "$img_path" || log_and_exit "Manual truncate failed"
    loop_device=$(losetup -f)
    losetup "${loop_device}" "$img_path" || log_and_exit "Manual losetup failed"
    mkfs.ext4 -F -b 4096 -O ^metadata_csum,^64bit,^bigalloc -O has_journal,ext_attr,resize_inode,dir_index,filetype,extent,flex_bg,sparse_super,large_file,huge_file,inline_data,uninit_bg,fast_commit "${loop_device}" || log_and_exit "Manual mkfs.ext4 failed"
    tune2fs -m 0 "${loop_device}" || log_and_exit "Manual tune2fs failed"
    mkdir -p "${dist_dir}/emby-xy"
    mount -o noatime,nodiratime,data=writeback "${loop_device}" "${dist_dir}/emby-xy" || log_and_exit "Manual mount failed"
    mkdir -p "${dist_dir}/emby-xy/xiaoya" && chmod -R 777 "${dist_dir}/emby-xy/xiaoya"

    # File selection (use keys from FILE_TO_DIR_MAP for consistency)
    # These are the files the user can choose to download.
    local file_options_manual=("all.mp4" "pikpak.mp4" "115.mp4" "config.mp4" "json.mp4" "短剧.mp4" "蓝光原盘.mp4")
    declare -A selected_files_manual
    for file in "${file_options_manual[@]}"; do selected_files_manual[$file]=1; done # Default all selected

    echo -e "\n请选择要处理的文件（默认全选）："
    while true; do
        for index in "${!file_options_manual[@]}"; do
            file="${file_options_manual[$index]}"
            status_char="×"; color="$Red"
            if [ "${selected_files_manual[$file]}" -eq 1 ]; then status_char="√"; color="$Green"; fi
            printf "[ %-1d ] ${color}[%s] %s${NC}\n" $((index + 1)) "$status_char" "$file"
        done
        printf "[ 0 ] 确认并继续\n"
        read -erp "请输入序号(0-${#file_options_manual[@]}): " select_num_manual
        if [[ "$select_num_manual" =~ ^[0-9]+$ ]]; then
            if [ "$select_num_manual" -eq 0 ]; then
                local count_selected=0
                for file in "${file_options_manual[@]}"; do if [ "${selected_files_manual[$file]}" -eq 1 ]; then let count_selected+=1; fi; done
                if [ $count_selected -eq 0 ]; then ERROR "至少选择一个文件"; else break; fi
            elif [ "$select_num_manual" -ge 1 ] && [ "$select_num_manual" -le ${#file_options_manual[@]} ]; then
                file="${file_options_manual[$((select_num_manual-1))]}"
                selected_files_manual[$file]=$((1 - selected_files_manual[$file]))
            else ERROR "无效序号"; fi
        else ERROR "无效输入"; fi
    done
    
    # Prepare intermediate dir and source files (manual mode specific)
    auto_mode_prepare_intermediate_dir_and_source_files # Re-use this logic, but files_to_process_list needs to be populated from selected_files_manual
    files_to_process_list=()
    for file_key in "${!selected_files_manual[@]}"; do
        if [ "${selected_files_manual[$file_key]}" -eq 1 ]; then
            files_to_process_list+=("$file_key")
        fi
    done


    # Download (using intermediate_dir)
    INFO "Manual Mode: Starting download and extraction to $intermediate_dir..."
    for file_key_to_download in "${!selected_files_manual[@]}"; do
        if [ "${selected_files_manual[$file_key_to_download]}" -eq 1 ]; then
            INFO "Processing file for download: $file_key_to_download"
            
            local actual_download_name_on_server="$file_key_to_download"
            # If user selected "config.mp4", we actually download "config.new.mp4" from server
            # but save it as "config.mp4" locally in source_dir/temp, then extract to intermediate_dir/config
            # if [ "$file_key_to_download" == "config.mp4" ]; then
            #     actual_download_name_on_server="config.new.mp4"
            #     INFO "Will download 'config.new.mp4' from server as '$file_key_to_download' for extraction."
            # fi

            docker run --rm --net=host \
                -v "${source_dir}/temp:/source" \
                -v "${intermediate_dir}:/dist" \
                ailg/ggbond:latest \
                bash -c "cd /source && \
                        echo 'Downloading ${actual_download_name_on_server} to ${file_key_to_download}...' && \
                        aria2c -o \"${file_key_to_download}\" --auto-file-renaming=false --allow-overwrite=true -c -x6 \"${xiaoya_addr}/d/元数据/${actual_download_name_on_server}\" && \
                        echo 'Extracting ${file_key_to_download}...' && \
                        if [ \"$file_key_to_download\" = \"config.mp4\" ]; then \
                            7z x -aoa -bb1 -mmt=16 \"${file_key_to_download}\" -o\"/dist/\" ; \
                        else \
                            7z x -aoa -bb1 -mmt=16 \"${file_key_to_download}\" -o\"/dist/xiaoya\" ; \
                        fi"
            if [ $? -eq 0 ]; then INFO "√ $file_key_to_download processed."; else log_and_exit "× $file_key_to_download failed."; fi
        fi
    done

    INFO "Manual Mode: Rsyncing initial files (excluding PikPak) from $intermediate_dir to ${dist_dir}/emby-xy..."
    rsync -av --progress --exclude="PikPak" "${intermediate_dir}/xiaoya/" "${dist_dir}/emby-xy/xiaoya/" || log_and_exit "Manual initial rsync failed"
    # Note: config rsync is handled inside _perform_image_processing_and_finalization if config.mp4 was downloaded

    # --- Call common processing function ---
    ver=$(date +%y%m%d)
    base_img_filename=$(basename "$img_path")
    # Ensure JELLYFIN_MODE is set based on img_name for manual mode
    if [[ "$img_name" == *"jellyfin"* ]]; then
        JELLYFIN_MODE=true
    else
        JELLYFIN_MODE=false
    fi
    INFO "Manual mode calling common processing. JELLYFIN_MODE=${JELLYFIN_MODE}"
    _perform_image_processing_and_finalization
    # --- End of common processing call ---

    INFO "Manual mode processing complete."
    INFO "Final image(s) and .mp4 files should be in: ${nopp_noconfig_dir:-/mnt/disks/192.168.9.233_m4/my_img/emby/ver_${ver}}"
    INFO "Loop device ${loop_device} should be detached. Mount point ${dist_dir}/emby-xy should be unmounted by cleanup."
    INFO "Please check logs for specific final file paths and any cleanup messages."
}


# --- Script Entry Point ---
main() {
    # Check essential commands first, universally
    local universal_required_commands=("curl" "losetup" "mount" "umount" "mkdir" "rm" "df" "stat" "ip" "tee" "date" "docker" "7z" "truncate" "mkfs.ext4" "tune2fs" "e2fsck" "resize2fs" "rsync" "find" "sed" "cat" "du" "awk" "grep" "tail" "basename" "dirname" "chmod")
    for cmd_check in "${universal_required_commands[@]}"; do
        check_command "$cmd_check" # This will log_and_exit if not found
    done
    
    parse_args "$@" # Sets AUTO_MODE and JELLYFIN_MODE

    if [ "$AUTO_MODE" = true ]; then
        auto_mode_main
    else
        manual_mode_main
    fi

    INFO "Script finished."
}

main "$@"
