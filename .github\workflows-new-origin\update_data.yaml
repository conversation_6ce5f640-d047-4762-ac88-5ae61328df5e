name: Update <PERSON>ya Data Files

on:
  schedule:
    - cron: '0 */6 * * *'  # 每6小时运行一次
  workflow_dispatch:

jobs:
  update-files:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout Repository
      uses: actions/checkout@v3
    
    - name: Setup Git credentials
      run: |
        git config --global user.email "github-actions[bot]@users.noreply.github.com"
        git config --global user.name "github-actions[bot]"
        git config --global credential.helper store
        echo "https://${{ secrets.REPO_AILG333 }}:<EMAIL>" > ~/.git-credentials

    - name: Install wget
      run: sudo apt-get install wget -y

    - name: Check and Update Files
      run: |
        remote_ver=$(wget -qO- --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" -T 30 -t 2 http://docker.xiaoya.pro/update/version.txt || \
                     wget -qO- --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" --header="Host:docker.xiaoya.pro" -T 30 -t 2 http://*************/update/version.txt || \
                     wget -qO- -T 30 -t 2 https://github.com/xiaoyaliu00/data/raw/main/version.txt || \
                     wget -qO- -T 30 -t 2 http://data.har01d.cn/version.txt)

        if [ -z "$remote_ver" ]; then
          echo "找不到有效下载地址"
          exit 1
        fi

        data_dir="data"
        mkdir -p "${data_dir}"
        touch "${data_dir}/version.txt"
        local_ver=$(cat "${data_dir}/version.txt")
        echo "remote_ver is: ${remote_ver}"
        echo "local_ver is: ${local_ver}"
        if [ "$local_ver"x != "$remote_ver"x ] || [ ! -f "${data_dir}/tvbox.zip" ] || [ ! -f "${data_dir}/update.zip" ] || [ ! -f "${data_dir}/index.zip" ]; then
          echo "最新版本 $remote_ver 开始更新下载....."
          if wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" -T 30 -t 2 http://docker.xiaoya.pro/update/tvbox.zip -O "${data_dir}/tvbox.zip" || \
             wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" --header="Host:docker.xiaoya.pro" -T 30 -t 2 http://*************/update/tvbox.zip -O "${data_dir}/tvbox.zip" || \
             wget -T 30 -t 2 https://github.com/xiaoyaliu00/data/raw/main/tvbox.zip -O "${data_dir}/tvbox.zip" || \
             wget -T 30 -t 2 http://data.har01d.cn/tvbox.zip -O "${data_dir}/tvbox.zip" || \
             cp /tvbox.zip ${data_dir}/tvbox.zip; then echo "成功更新 tvbox.zip"; fi

          if wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" -T 30 -t 2 http://docker.xiaoya.pro/update/update.zip -O "${data_dir}/update.zip" || \
             wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" --header="Host:docker.xiaoya.pro" -T 30 -t 2 http://*************/update/update.zip -O "${data_dir}/update.zip" || \
             wget -T 30 -t 2 https://github.com/xiaoyaliu00/data/raw/main/update.zip -O "${data_dir}/update.zip" || \
             wget -T 30 -t 2 http://data.har01d.cn/update.zip -O "${data_dir}/update.zip"; then echo "成功更新 update.zip"; fi

          if wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" -T 30 -t 2 http://docker.xiaoya.pro/update/index.zip -O "${data_dir}/index.zip" || \
             wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" --header="Host:docker.xiaoya.pro" -T 30 -t 2 http://*************/update/index.zip -O "${data_dir}/index.zip" || \
             wget -T 30 -t 2 https://github.com/xiaoyaliu00/data/raw/main/index.zip -O "${data_dir}/index.zip" || \
             wget -T 30 -t 2 http://data.har01d.cn/index.zip -O "${data_dir}/index.zip"; then echo "成功更新 index.zip"; fi

          if wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" -T 30 -t 2 http://docker.xiaoya.pro/update/version.txt -O "${data_dir}/version.txt" || \
             wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" --header="Host:docker.xiaoya.pro" -T 30 -t 2 http://*************/update/version.txt -O "${data_dir}/version.txt" || \
             wget -T 30 -t 2 https://github.com/xiaoyaliu00/data/raw/main/version.txt -O "${data_dir}/version.txt" || \
             wget -T 30 -t 2 http://data.har01d.cn/version.txt -O "${data_dir}/version.txt"; then echo "成功更新 version.txt"; fi
        else
          echo "数据版本已经是最新的无须更新"
        fi

        mkdir -p sync_files
        cp -f data/* sync_files/

    - name: Download and process share lists
      run: |
        # 创建目录
        mkdir -p sync_files
        
        # 尝试从第一个地址下载脚本文件
        if ! curl --insecure -fsSL https://ddsrem.com/xiaoya/all_in_one.sh -o all_in_one.sh; then
          # 如果第一个地址失败，尝试第二个地址
          curl --insecure -fsSL https://fastly.jsdelivr.net/gh/xiaoyaDev/xiaoya-alist@latest/all_in_one.sh -o all_in_one.sh
        fi
        
        # 创建哈希文件目录
        mkdir -p .hash
        
        # 处理 pikpakshare_list
        pikpakshare_list_base64=$(grep -o 'pikpakshare_list_base64="[^"]*"' all_in_one.sh | cut -d'"' -f2)
        if [ -n "$pikpakshare_list_base64" ]; then
          # 计算当前 base64 字符串的 MD5 哈希值
          current_hash=$(echo "$pikpakshare_list_base64" | md5sum | cut -d' ' -f1)
          
          # 检查是否存在上次的哈希值
          if [ ! -f .hash/pikpakshare_hash.md5 ] || [ "$(cat .hash/pikpakshare_hash.md5)" != "$current_hash" ]; then
            echo "$pikpakshare_list_base64" | base64 --decode > sync_files/pikpakshare_list.txt
            echo "已更新 pikpakshare_list.txt (哈希值变化)"
            # 保存当前哈希值
            echo "$current_hash" > .hash/pikpakshare_hash.md5
          else
            echo "pikpakshare_list.txt 未变化，跳过更新"
          fi
        else
          echo "未找到 pikpakshare_list_base64 内容"
        fi
        
        # 处理 pan115share_list
        pan115share_list_base64=$(grep -o 'pan115share_list_base64="[^"]*"' all_in_one.sh | cut -d'"' -f2)
        if [ -n "$pan115share_list_base64" ]; then
          # 计算当前 base64 字符串的 MD5 哈希值
          current_hash=$(echo "$pan115share_list_base64" | md5sum | cut -d' ' -f1)
          
          # 检查是否存在上次的哈希值
          if [ ! -f .hash/pan115share_hash.md5 ] || [ "$(cat .hash/pan115share_hash.md5)" != "$current_hash" ]; then
            echo "$pan115share_list_base64" | base64 --decode > sync_files/115share_list.txt
            echo "已更新 115share_list.txt (哈希值变化)"
            # 保存当前哈希值
            echo "$current_hash" > .hash/pan115share_hash.md5
          else
            echo "115share_list.txt 未变化，跳过更新"
          fi
        else
          echo "未找到 pan115share_list_base64 内容"
        fi
        
        # 处理 quarkshare_list
        quarkshare_list_base64=$(grep -o 'quarkshare_list_base64="[^"]*"' all_in_one.sh | cut -d'"' -f2)
        if [ -n "$quarkshare_list_base64" ]; then
          # 计算当前 base64 字符串的 MD5 哈希值
          current_hash=$(echo "$quarkshare_list_base64" | md5sum | cut -d' ' -f1)
          
          # 检查是否存在上次的哈希值
          if [ ! -f .hash/quarkshare_hash.md5 ] || [ "$(cat .hash/quarkshare_hash.md5)" != "$current_hash" ]; then
            echo "$quarkshare_list_base64" | base64 --decode > sync_files/quarkshare_list.txt
            echo "已更新 quarkshare_list.txt (哈希值变化)"
            # 保存当前哈希值
            echo "$current_hash" > .hash/quarkshare_hash.md5
          else
            echo "quarkshare_list.txt 未变化，跳过更新"
          fi
        else
          echo "未找到 quarkshare_list_base64 内容"
        fi
        
        # 将哈希文件添加到 git
        git add .hash/

    - name: Commit and Push Changes
      run: |
        git add .
        git commit -m "Update Xiaoya Data files" || echo "No changes to commit"
        git push origin master
