<?xml version="1.0"?>
<Container version="2">
  <Name>g-box</Name>
  <Repository>ailg/g-box:hostmode</Repository>
  <Network>host</Network>
  <MyIP/>
  <Shell>bash</Shell>
  <Privileged>false</Privileged>
  <Support>https://ailg.ggbond.org</Support>
  <Project/>
  <Overview>g-box是在alist-tvbox项目基础上魔改的应用，内置了完整的小雅alist，并可以实现快速安装小雅emby/Jellyfin全家桶，同时内置当前主流tvbox订阅源（如pg、饭太硬等），实现了一些原版alist-tvbox暂时没做的功能，比如打开阿里快传115后的调用第三方播放和小雅emby中的直接播放，内置到Emby应用中的”十全大补瓜“播放器，以及直接在web中获取115的cookie等等……</Overview>
  <Category>MediaServer:Video MediaServer:Music MediaServer:Photos</Category>
  <WebUI>http://[IP]:[PORT:4567]/</WebUI>
  <TemplateURL>https://gbox.ggbond.org/gbox.xml</TemplateURL>
  <Icon>https://gbox.ggbond.org/gbox_logo.png</Icon>
  <ExtraParams></ExtraParams>
  <PostArgs/>
  <CPUset/>
  <DateInstalled>1703839641</DateInstalled>
  <DonateText/>
  <DonateLink/>
  <Requires/>
  <Config Name="G-box data dir" Target="/data" Default="" Mode="rw" Description="Config directory" Type="Path" Display="always" Required="true" Mask="false"></Config>
  <Config Name="Restart Policy" Target="--restart" Default="always" Mode="" Description="" Type="Variable" Display="always" Required="false" Mask="false">always</Config>
</Container>
