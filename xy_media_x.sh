#!/bin/bash
# shellcheck shell=bash
# shellcheck disable=SC2086

PATH=${PATH}:/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin:~/bin:/opt/homebrew/bin
export PATH

Blue="\033[1;34m"
Green="\033[1;32m"
Red="\033[1;31m"
Yellow="\033[1;33m"
NC="\033[0m"
INFO="[${Green}INFO${NC}]"
ERROR="[${Red}ERROR${NC}]"
WARN="[${Yellow}WARN${NC}]"

function INFO() {
    echo -e "${INFO} ${1}"
}
function ERROR() {
    echo -e "${ERROR} ${1}"
}
function WARN() {
    echo -e "${WARN} ${1}"
}

# 检查命令是否存在
check_command() {
    local cmd=$1
    if ! command -v "$cmd" &> /dev/null; then
        echo -e "${RED}错误: 命令 '$cmd' 未找到。请安装后重试。${NC}"
        return 1
    fi
    return 0
}

# 清理函数
cleanup() {
    echo -e "${INFO} 开始清理..."
    umount "${loop_device}" || umount "${dist_dir}/emby-xy"
    losetup -d "${loop_device}"
}

# 检查必需的命令
required_commands=("aria2c" "du" "7z" "curl" "exp_ailg" "losetup" "mount")
for cmd in "${required_commands[@]}"; do
    if ! check_command "$cmd"; then
        cleanup
        exit 1
    fi
done

# 获取用户输入的预期空间大小
read -erp "请输入预期空间大小(GB，建议为实际文件大小的150%): " set_size
while true; do
    if [[ "$set_size" =~ ^[0-9]+$ ]]; then
        break
    else
        echo -e "${RED}错误: 请输入有效的数字${NC}"
        read -erp "请输入预期空间大小(GB，建议为实际文件大小的150%): " set_size
    fi
done

# 获取源目录
read -erp "请输入源目录路径: " source_dir
if [ ! -d "$source_dir" ]; then
    read -erp "目录不存在，是否创建? (Y/n): " create_dir
    if [[ "$create_dir" =~ ^[Yy]$ ]]; then
        mkdir -p "$source_dir"
    else
        cleanup
        exit 1
    fi
fi

# 检查源目录空间
source_space=$(df -BG --output=avail "$(dirname "$source_dir")" | tail -n1 | cut -d'G' -f1)
if [ "$source_space" -lt "$set_size" ]; then
    echo -e "${RED}警告: 源目录所在磁盘空间不足${NC}"
    cleanup
    exit 1
fi

# 获取目标目录
read -erp "请输入目标目录路径: " dist_dir
if [ ! -d "$dist_dir" ]; then
    read -erp "目录不存在，是否创建? (Y/n): " create_dir
    if [[ "$create_dir" =~ ^[Yy]$ ]]; then
        mkdir -p "$dist_dir"
    else
        cleanup
        exit 1
    fi
fi

# 检查源目录和目标目录是否在同一磁盘
source_dev=$(stat -c '%d' "$source_dir")
dist_dev=$(stat -c '%d' "$dist_dir")

if [ "$source_dev" == "$dist_dev" ]; then
    echo -e "${YELLOW}警告: 源目录和目标目录在同一文件系统上${NC}"
    read -erp "不建议在同一文件系统上，是否继续? (Y/n): " continue_same_disk
    if [[ ! "$continue_same_disk" =~ ^[Yy]$ ]]; then
        cleanup
        exit 1
    fi
fi

# 选择镜像名
img_options=(
    "emby-ailg-115.img"
    "emby-ailg-lite-115.img"
    "jellyfin-ailg-115.img"
    "jellyfin-ailg-lite-115.img"
)

echo -e "\033[1;37m请选择镜像名：\033[0m"
for index in "${!img_options[@]}"; do
    printf "[ %-1d ] \033[1;33m%s\033[0m\n" $((index + 1)) "${img_options[$index]}"
done
printf "[ 0 ] \033[1;33m自定义镜像名\033[0m\n"

while :; do
    read -erp "请输入序号: " img_select
    if [ "${img_select}" -gt 0 ] && [ "${img_select}" -le ${#img_options[@]} ]; then
        img_name="${img_options[$((img_select - 1))]}"
        break
    elif [ "${img_select}" -eq 0 ]; then
        read -erp "请输入自定义镜像名: " img_name
        break
    else
        echo -e "${RED}错误: 请输入0到${#img_options[@]}之间的数字${NC}"
    fi
done

echo -e "${INFO} 已选择镜像: ${img_name}"

# 检查和设置xiaoya地址
docker0=$(ip address | grep inet | grep -v 172.17 | grep -v 169. | grep -v 127.0.0.1 | grep -v inet6 | awk '{print $2}' | sed 's/addr://' | head -n1 | cut -f1 -d"/")
echo -e "测试xiaoya的联通性..."
if curl -siL "http://${docker0}:5678/d/README.md" | grep -v 302 | grep -q "x-oss-"; then
    xiaoya_addr="http://${docker0}:5678"
    read -erp "找到小雅alist地址：${xiaoya_addr} ，确认请按Y/y，或按N/n手动输入：" user_confirm
    if [[ ! "$user_confirm" =~ ^[Yy]$ ]]; then
        read -erp "请输入小雅alist地址：" xiaoya_addr
    fi
else
    read -erp "未找到小雅alist地址，请手动输入：" xiaoya_addr
fi

# 验证输入的地址
if ! curl -sIL "${xiaoya_addr}/d/README.md" | grep -v 302 | grep -q "x-oss-"; then
    echo -e "${RED}错误: 无法连接到小雅alist${NC}"
    cleanup
    exit 1
fi

# 创建和挂载镜像
img_path="${dist_dir}/${img_name}"
echo "创建镜像文件..."
truncate -s "${set_size}G" "${img_path}"

echo "初始化镜像文件..."
mkfs.ext4 "${img_path}"
loop_device=$(losetup -f)
losetup -P "${loop_device}" "${img_path}"
e2fsck -f -y "${loop_device}"
resize2fs "${loop_device}"
mkdir -p "${dist_dir}/emby-xy"
mount "${loop_device}" "${dist_dir}/emby-xy"

# 定义文件列表
file_options=(
    "all.mp4"
    "pikpak.mp4"
    "115.mp4"
    "config.new.mp4"
)

# 创建一个关联数组来跟踪文件是否被选中（默认全选）
declare -A selected_files
for file in "${file_options[@]}"; do
    selected_files[$file]=1
done

echo -e "\n${INFO} 当前所有可处理的文件（默认全选）："
echo -e "${YELLOW}提示：输入序号可以取消选择对应文件，再次输入可重新选择${NC}"
while true; do
    # 显示文件列表和当前状态
    echo -e "\n当前文件选择状态："
    for index in "${!file_options[@]}"; do
        file="${file_options[$index]}"
        if [ "${selected_files[$file]}" -eq 1 ]; then
            printf "[ %-1d ] \033[1;32m[√] %s\033[0m\n" $((index + 1)) "$file"
        else
            printf "[ %-1d ] \033[1;31m[×] %s\033[0m\n" $((index + 1)) "$file"
        fi
    done
    printf "[ 0 ] \033[1;33m确认并继续\033[0m\n"

    read -erp "请输入序号(0-${#file_options[@]}): " select_num
    
    if [ "$select_num" -eq 0 ]; then
        # 检查是否至少选择了一个文件
        selected_count=0
        for file in "${file_options[@]}"; do
            if [ "${selected_files[$file]}" -eq 1 ]; then
                ((selected_count++))
            fi
        done
        
        if [ $selected_count -eq 0 ]; then
            echo -e "${RED}错误: 至少需要选择一个文件进行处理${NC}"
            continue
        fi
        break
    elif [ "$select_num" -ge 1 ] && [ "$select_num" -le ${#file_options[@]} ]; then
        # 切换选中状态
        file="${file_options[$((select_num-1))]}"
        if [ "${selected_files[$file]}" -eq 1 ]; then
            selected_files[$file]=0
        else
            selected_files[$file]=1
        fi
    else
        echo -e "${RED}错误: 无效的序号${NC}"
    fi
done

# 检查挂载点并解压文件
if ! mount | grep -q " ${dist_dir}/emby-xy "; then
    echo -e "${RED}错误: ${dist_dir}/emby-xy 未正确挂载${NC}"
    cleanup
    exit 1
fi

mkdir -p "${dist_dir}/emby-xy/xiaoya"
chmod -R 777 "${dist_dir}/emby-xy/xiaoya"

# 下载选中的文件
echo -e "\n${INFO} 开始处理选中的文件..."
for file in "${file_options[@]}"; do
    if [ "${selected_files[$file]}" -eq 1 ]; then
        echo -e "${INFO} 正在处理: $file"
        docker run --rm --net=host \
            -v "${source_dir}:/source" \
            -v "${dist_dir}/emby-xy:/dist" \
            ailg/ggbond:latest \
            bash -c "cd /source && \
                    aria2c -o \"${file}\" --auto-file-renaming=false --allow-overwrite=true -c -x6 \"${xiaoya_addr}/d/元数据/${file}\" && \
                    if [ \"$file\" = \"config.new.mp4\" ]; then \
                        7z x -aoa -bb1 -mmt=16 \"${file}\" -o\"/dist/\" ; \
                    else \
                        7z x -aoa -bb1 -mmt=16 \"${file}\" -o\"/dist/xiaoya\" ; \
                    fi"
        
        if [ $? -eq 0 ]; then
            echo -e "${Green}√ $file 处理完成${NC}"
        else
            echo -e "${Red}× $file 处理失败${NC}"
            cleanup
            exit 1
        fi
    fi
done

# 运行Python处理脚本
echo "开始运行Python处理脚本..."
docker run -v "${dist_dir}/emby-xy:/ailg" ailg/py-xy:latest python /ailg/process_media.py

echo -e "${GREEN}处理完成${NC}"