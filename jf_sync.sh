#!/bin/bash

# 让用户输入A和B两个目录的路径
read -r -p "请输入源目录A的路径: " source_dir
read -r -p "请输入目标目录B的路径: " target_dir

# 检查目录是否存在
if [ ! -d "$source_dir" ]; then
  echo "源目录A不存在"
  exit 1
fi

if [ ! -d "$target_dir" ]; then
  echo "目标目录B不存在"
  exit 1
fi

# 遍历A中的所有目录
find "$source_dir" -type d | while IFS= read -r dir; do
  relative_path="${dir#"$source_dir"}"
  target_subdir="$target_dir$relative_path"

  if [ -d "$target_subdir" ]; then
    # 检查A目录下是否存在.strm文件
    strm_files=$(find "$dir" -maxdepth 1 -name "*.strm")
    if [ -n "$strm_files" ]; then
      # 替换B中的strm文件
      for strm_file in $strm_files; do
        strm_filename=$(basename "$strm_file")
        target_strm_file="$target_subdir/$strm_filename"
        nfo_file="${strm_file%.strm}.nfo"
        if [ -f "$target_strm_file" ] && [ -f "$nfo_file" ]; then
          cp -f "$strm_file" "$target_strm_file" # 强制覆盖已存在的同名文件
        elif [ ! -f "$nfo_file" ]; then
          cp -r "$dir" "$target_subdir/" # 复制整个A的当前目录到B
        fi
      done
    fi
  else
    # 如果B中不存在相对目录，复制整个A的当前目录到B
    mkdir -p "$target_subdir"
    cp -r "$dir/"* "$target_subdir/"
  fi
done

# 遍历B中的所有目录，删除多余的strm文件和目录
find "$target_dir" -type d | while IFS= read -r dir; do
  strm_files=$(find "$dir" -maxdepth 1 -name "*.strm")
  if [ -n "$strm_files" ]; then
    for strm_file in $strm_files; do
      relative_path="${strm_file#"$target_dir"}"
      source_strm_file="$source_dir$relative_path"
      if [ ! -f "$source_strm_file" ]; then
        # 检查是否是唯一的strm文件
        strm_count=$(find "$dir" -maxdepth 1 -name "*.strm" | wc -l)
        if [ "$strm_count" -eq 1 ]; then
          rm -r "$dir"
          break
        else
          rm "$strm_file"
          nfo_file="${strm_file%.strm}.nfo"
          jpg_file="${strm_file%.strm}.jpg"
          [ -f "$nfo_file" ] && rm "$nfo_file"
          [ -f "$jpg_file" ] && rm "$jpg_file"
        fi
      fi
    done
  fi

  # 删除空目录
  if [ -z "$(ls -A "$dir")" ]; then
    rmdir "$dir"
  fi
done

echo "同步完成"

