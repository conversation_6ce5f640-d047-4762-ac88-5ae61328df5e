#!/bin/bash
if [ -f /etc/synoinfo.conf ];then
		OSNAME='synology'
elif [ -f /etc/unraid-version ];then
    OSNAME='unraid'
elif command -v crontab >/dev/null 2>&1 && ps -ef | grep '[c]rond' >/dev/null 2>&1; then
    OSNAME='other'
else
    echo -e "\033[1;33m您的系统不支持crontab，需要手动配置开机自启挂载，如您不会请用常规方式安装，按CTRL+C退出！\033[0m"
fi
emby_img=$(basename "$1")
image_dir=${1%/*}
media_dir=$2
loop_device=$(losetup -l | grep -E "${emby_img}" | cut -d' ' -f1)
for i in {1..3};do
    mount | grep ${loop_device} > /dev/null 2>&1 && umount "${loop_device}" || break
done    
mount | grep -qF "$2" && umount "$2"
[ "$(ls -A "$2")" ] && echo -e "\033[1;31m${2}目录非空或卸载失败，程序退出！\033[0m" && exit 1
if [ ! -z "${loop_device}" ];then
    mount "${loop_device}" "$2"
else
    docker run -i --privileged --name=ailg --net=host -v "${image_dir}":/ailg -v "$media_dir":/mount_emby ailg/ggbond:test \
    exp_ailg -m "/ailg/$emby_img" "/mount_emby"
    #loop_device=$(losetup -l | grep -E "${emby_img}" | cut -d' ' -f1)
    loop_device=/dev/loop7
    mount "${loop_device}" "${media_dir}"
    docker stop ailg && docker rm ailg
fi

COMMAND="/usr/bin/mount_ailg -m \"$1\" \"$2\""
if [[ $OSNAME == "synology" ]];then
    if ! grep -qF -- "$COMMAND" /etc/rc.local; then
        cp -f /etc/rc.local /etc/rc.local.bak
        sed -i '/mount_ailg/d' /etc/rc.local
        if grep -q 'exit 0' /etc/rc.local; then
            sed -i "/exit 0/i\/usr/bin/mount_ailg -m \"$1\" \"$2\"" /etc/rc.local
        else
            echo -e "/usr/bin/mount_ailg -m \"$1\" \"$2\"" >> /etc/rc.local
        fi
    fi
elif [[ $OSNAME == "unraid" ]];then
    if ! grep -qF -- "$COMMAND" /boot/config/go; then
        echo -e "/usr/bin/mount_ailg -m \"$1\" \"$2\"" >> /boot/config/go
    fi
elif [[ $OSNAME == "other" ]];then
    CRON="@reboot /usr/bin/mount_ailg -m \"$1\" \"$2\""
    crontab -l | grep -v mount_ailg > /tmp/cronjob.tmp
    echo -e "${CRON}" >> /tmp/cronjob.tmp
    crontab /tmp/cronjob.tmp
else
    echo -e "以下命令请自行配置开机自启动：\033[1;33m/usr/bin/mount_ailg -m \"$1\" \"$2\"\033[0m"
fi
