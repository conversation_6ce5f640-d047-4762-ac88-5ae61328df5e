#!/bin/bash
# shellcheck shell=bash
# shellcheck disable=SC2086

PATH=${PATH}:/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin:~/bin:/opt/homebrew/bin
export PATH

Blue="\033[1;34m"
Green="\033[1;32m"
Red="\033[1;31m"
Yellow="\033[1;33m"
NC="\033[0m"
INFO="[${Green}INFO${NC}]"
ERROR="[${Red}ERROR${NC}]"
WARN="[${Yellow}WARN${NC}]"

function INFO() {
    echo -e "${INFO} ${1}"
}
function ERROR() {
    echo -e "${ERROR} ${1}"
}
function WARN() {
    echo -e "${WARN} ${1}"
}

if [ -z "${1}" ]; then
    WARN "未输入apk文件的名字"
    exit 1
else
    apk_name="$1"
fi

apk_dir=${apk_name%.apk}
apktool d "${apk_name}"
apktool b "${apk_dir}" -o "${apk_nosign:=${apk_dir}_ns.apk}"
zipalign -v 4 "${apk_nosign}" "${apk_nz:=${apk_nosign}_z.apk}"
manifest_file="$apk_dir/AndroidManifest.xml"

if [ -f "$manifest_file" ]; then
    if grep -q 'android:extractNativeLibs="false"' "$manifest_file"; then
        sed -i 's/android:extractNativeLibs="false"/android:extractNativeLibs="true"/' "$manifest_file"
    else
        sed -i '/<application/ s/<application/<application android:extractNativeLibs="true"/' "$manifest_file"
    fi
else
    echo "AndroidManifest.xml 不存在于 $apk_dir"
fi

apksigner sign --ks abc.keystore --ks-key-alias abc.keystore --ks-pass pass:123456 --key-pass pass:123456 --out "${apk_sign:=${apk_dir}_sign.apk}" "${apk_nz}"
chmod 777 "${apk_sign}"


