{"code": 200, "message": "success", "data": {"AList V2": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "root_folder_path", "type": "string", "default": "/", "options": "", "required": true, "help": ""}, {"name": "url", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "password", "type": "string", "default": "", "options": "", "required": false, "help": ""}, {"name": "access_token", "type": "string", "default": "", "options": "", "required": false, "help": ""}], "config": {"name": "AList V2", "local_sort": true, "only_local": false, "only_proxy": false, "no_cache": false, "no_upload": true, "need_ms": false, "default_root": "/", "alert": ""}}, "AList V3": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "root_folder_path", "type": "string", "default": "/", "options": "", "required": true, "help": ""}, {"name": "url", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "password", "type": "string", "default": "", "options": "", "required": false, "help": ""}, {"name": "access_token", "type": "string", "default": "", "options": "", "required": false, "help": ""}], "config": {"name": "AList V3", "local_sort": true, "only_local": false, "only_proxy": false, "no_cache": false, "no_upload": false, "need_ms": false, "default_root": "/", "alert": ""}}, "Alias": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "paths", "type": "text", "default": "", "options": "", "required": true, "help": ""}], "config": {"name": "<PERSON><PERSON>", "local_sort": true, "only_local": false, "only_proxy": false, "no_cache": true, "no_upload": true, "need_ms": false, "default_root": "/", "alert": ""}}, "AliyundriveOpen": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "root_folder_id", "type": "string", "default": "root", "options": "", "required": true, "help": ""}, {"name": "refresh_token", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order_by", "type": "select", "default": "name", "options": "name,size,updated_at,created_at", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "ASC", "options": "ASC,DESC", "required": false, "help": ""}, {"name": "oauth_token_url", "type": "string", "default": "https://api.nn.ci/alist/ali_open/token", "options": "", "required": false, "help": ""}, {"name": "client_id", "type": "string", "default": "", "options": "", "required": false, "help": "Keep it empty if you don't have one"}, {"name": "client_secret", "type": "string", "default": "", "options": "", "required": false, "help": "Keep it empty if you don't have one"}, {"name": "remove_way", "type": "select", "default": "", "options": "trash,delete", "required": true, "help": ""}, {"name": "internal_upload", "type": "bool", "default": "", "options": "", "required": false, "help": "If you are using Aliyun ECS is located in Beijing, you can turn it on to boost the upload speed"}, {"name": "rorb", "type": "string", "default": "r", "options": "", "required": false, "help": ""}], "config": {"name": "AliyundriveOpen", "local_sort": false, "only_local": false, "only_proxy": false, "no_cache": false, "no_upload": false, "need_ms": false, "default_root": "root", "alert": ""}}, "AliyundriveShare2Open": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "RefreshToken", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "RefreshTokenOpen", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "TempTransferFolderID", "type": "string", "default": "root", "options": "", "required": false, "help": ""}, {"name": "share_id", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "share_pwd", "type": "string", "default": "", "options": "", "required": false, "help": ""}, {"name": "root_folder_id", "type": "string", "default": "root", "options": "", "required": true, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,updated_at,created_at", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "ASC,DESC", "required": false, "help": ""}, {"name": "oauth_token_url", "type": "string", "default": "https://api.nn.ci/alist/ali_open/token", "options": "", "required": false, "help": ""}, {"name": "client_id", "type": "string", "default": "", "options": "", "required": false, "help": "Keep it empty if you don't have one"}, {"name": "client_secret", "type": "string", "default": "", "options": "", "required": false, "help": "Keep it empty if you don't have one"}, {"name": "rorb", "type": "string", "default": "r", "options": "", "required": false, "help": ""}], "config": {"name": "AliyundriveShare2Open", "local_sort": false, "only_local": false, "only_proxy": false, "no_cache": false, "no_upload": true, "need_ms": false, "default_root": "root", "alert": ""}}, "Onedrive": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "root_folder_path", "type": "string", "default": "/", "options": "", "required": true, "help": ""}, {"name": "region", "type": "select", "default": "global", "options": "global,cn,us,de", "required": true, "help": ""}, {"name": "is_sharepoint", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "client_id", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "client_secret", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "redirect_uri", "type": "string", "default": "https://alist.nn.ci/tool/onedrive/callback", "options": "", "required": true, "help": ""}, {"name": "refresh_token", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "site_id", "type": "string", "default": "", "options": "", "required": false, "help": ""}, {"name": "chunk_size", "type": "number", "default": "5", "options": "", "required": false, "help": ""}], "config": {"name": "Onedrive", "local_sort": true, "only_local": false, "only_proxy": false, "no_cache": false, "no_upload": false, "need_ms": false, "default_root": "/", "alert": ""}}, "PikPak": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "root_folder_id", "type": "string", "default": "", "options": "", "required": false, "help": ""}, {"name": "username", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "password", "type": "string", "default": "", "options": "", "required": true, "help": ""}], "config": {"name": "PikPak", "local_sort": true, "only_local": false, "only_proxy": false, "no_cache": false, "no_upload": false, "need_ms": false, "default_root": "", "alert": ""}}, "PikPakShare": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "root_folder_id", "type": "string", "default": "", "options": "", "required": false, "help": ""}, {"name": "username", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "password", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "share_id", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "share_pwd", "type": "string", "default": "", "options": "", "required": false, "help": ""}], "config": {"name": "PikPakShare", "local_sort": true, "only_local": false, "only_proxy": false, "no_cache": false, "no_upload": true, "need_ms": false, "default_root": "", "alert": ""}}, "UrlTree": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "web_proxy", "type": "bool", "default": "", "options": "", "required": false, "help": ""}, {"name": "webdav_policy", "type": "select", "default": "302_redirect", "options": "302_redirect,use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "url_structure", "type": "text", "default": "https://jsd.nn.ci/gh/alist-org/alist/README.md\nhttps://jsd.nn.ci/gh/alist-org/alist/README_cn.md\nfolder:\n  CONTRIBUTING.md:1635:https://jsd.nn.ci/gh/alist-org/alist/CONTRIBUTING.md\n  CODE_OF_CONDUCT.md:2093:https://jsd.nn.ci/gh/alist-org/alist/CODE_OF_CONDUCT.md", "options": "", "required": true, "help": "structure:FolderName:\n  [FileName:][FileSize:][Modified:]Url"}, {"name": "head_size", "type": "bool", "default": "false", "options": "", "required": false, "help": "Use head method to get file size, but it may be failed."}], "config": {"name": "UrlTree", "local_sort": true, "only_local": false, "only_proxy": false, "no_cache": true, "no_upload": true, "need_ms": false, "default_root": "", "alert": ""}}, "WebDav": {"common": [{"name": "mount_path", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "order", "type": "number", "default": "", "options": "", "required": false, "help": "use to sort"}, {"name": "remark", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "cache_expiration", "type": "number", "default": "5", "options": "", "required": true, "help": "The cache expiration time for this storage"}, {"name": "webdav_policy", "type": "select", "default": "native_proxy", "options": "use_proxy_url,native_proxy", "required": true, "help": ""}, {"name": "down_proxy_url", "type": "text", "default": "", "options": "", "required": false, "help": ""}, {"name": "order_by", "type": "select", "default": "", "options": "name,size,modified", "required": false, "help": ""}, {"name": "order_direction", "type": "select", "default": "", "options": "asc,desc", "required": false, "help": ""}, {"name": "extract_folder", "type": "select", "default": "", "options": "front,back", "required": false, "help": ""}], "additional": [{"name": "vendor", "type": "select", "default": "other", "options": "sharepoint,other", "required": false, "help": ""}, {"name": "address", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "username", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "password", "type": "string", "default": "", "options": "", "required": true, "help": ""}, {"name": "root_folder_path", "type": "string", "default": "/", "options": "", "required": true, "help": ""}], "config": {"name": "WebDav", "local_sort": true, "only_local": false, "only_proxy": true, "no_cache": false, "no_upload": false, "need_ms": false, "default_root": "/", "alert": ""}}}}