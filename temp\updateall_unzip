#!/bin/sh

mkdir -p /var/lib/pxg
cd /var/lib/pxg
unzip -q /var/lib/data.zip
find ./ -type f -name "*.txt" -exec sed \-i "s/\r$//g" {} \;

if [ ! -f /opt/alist/data/data.db ]; then
	mv data.db /opt/alist/data/data.db
fi
if [ ! -f /data/pikpakshare_list.txt ]; then
    mv pikpakshare_list.txt /data/pikpakshare_list.txt
fi
mv config.json /opt/alist/data/config.json
mv token /token
mv guestpass /guestpass
mv pikpak /pikpak
mv checktoken /checktoken
mv checkopentoken /checkopentoken
mv check_quark_cookie.sh /check_quark_cookie.sh
mv reset /reset
mv ali_auto_checkin.sh /ali_auto_checkin.sh
mkdir -p /www/cgi-bin
mkdir -p /index
mv search /www/cgi-bin/search
mv sou /www/cgi-bin/sou
mv whatsnew /www/cgi-bin/whatsnew
mv header.html /www/cgi-bin/header.html
mv mobi.tgz /www/mobi.tgz
unzip -q -o /var/lib/http.d.okv2.zip -d /etc/nginx/http.d/
chmod -R 777 /etc/nginx/http.d
rm -f /var/lib/http.d.okv2.zip
cd /www/
tar zxf mobi.tgz
rm mobi.tgz
rm -rf /var/lib/pxg
cd /tmp/
rm *.zip 2>/dev/null

if [ -f /data/download_url.txt ]; then
	download_url=$(head -n1 /data/download_url.txt)
fi

check_cf=$(wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" --header="Host:docker.xiaoya.pro" -c -T 15 -t 5 --spider http://docker.xiaoya.pro/version.txt 2>&1|grep -i "remote file exists"|awk '{print $3}'|grep -v "^$")
if [[ $check_cf == "exists" ]]; then
        wget_para=' --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" -c -T 15 -t 5 http://docker.xiaoya.pro/'
else
    echo "无法连接CloudFlare服务器" 
        wget_para=' --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" -c -T 15 -t 5 http://docker.xiaoya.pro/'
fi

/bin/busybox-extras httpd -p 5233 -h /www
sleep 1
if [ -z "$download_url" ]; then
    echo "wget"$wget_para"version.txt"| /bin/sh
else
    echo "wget "$download_url"/version.txt" |/bin/sh
fi		

if [ ! -f version.txt ]; then
	cp /var/lib/version.txt /tmp/version.txt
fi
if [ ! -f version.txt ]; then
        echo "Failed to download version.txt file, the index file upgrade process has aborted"
else
	db_count=$(echo "select count() from x_storages;" | sqlite3 /opt/alist/data/data.db)
        remote=$(head -n1 version.txt)
        if [ ! -f /version.txt ]; then
                echo 0.0.0 > /version.txt
        fi
        local=$(head -n1 /version.txt)
        latest=$(printf "$remote\n$local\n" |sort -r |head -n1)
	if [[ $remote = $local ]] && [[ $db_count -gt 100 ]]; then
                echo `date` "current database version is updated, no need to upgrade"
        elif [[ $remote = $latest ]] || [[ $db_count -le 100 ]]; then
		if [ -z "$download_url" ]; then
			echo "wget"$wget_para"update/update.zip"| /bin/sh
		else
			echo "wget "$download_url"/update.zip"| /bin/sh
		fi
if [ ! -f update.zip ]; then
	cp /var/lib/update.zip /tmp/update.zip
fi
                if [ ! -f update.zip ]; then
                        echo "Failed to download update database file, the database upgrade process has aborted"
                else
                        unzip -o -q -P abcd update.zip
                        if [[ ! -f /data/pikpak.txt ]] || [[ ! -s /data/pikpak.txt ]]; then
                                sed -i "/PikPakShare/d" update.sql
                        fi
                        if grep '"account" "password"' /data/pikpak.txt; then
                                sed -i "/PikPakShare/d" update.sql
                        fi

                        echo `date` "total" $entries "records"
			if [ -f /opt/alist/data/data.db-shm ]; then
				rm /opt/alist/data/data.db-shm
			fi

			if [ -f /opt/alist/data/data.db-wal ]; then
                                rm /opt/alist/data/data.db-wal
                        fi
			
                        if [ ! -f /data/alist_auth_token.txt ]; then
                                random_token_tail=$(tr -dc '_A-Za-z0-9'  </dev/urandom  | head -c  64)
                                sed -i "s/0lSmqjgBRIMJakAkbJIE2KzO6h2CUVBuEkqrLiA5cJJzOzYxJtCTIGBXXnhrg7Av/$random_token_tail/" update.sql
                                echo "alist-09ceb38a-f143-47f7-b255-c3eec819cd7b"$random_token_tail > /data/alist_auth_token.txt
                        fi
                        if [ -f /data/alist_auth_token.txt ]; then
                                alist_auth_token=$(head -n1 /data/alist_auth_token.txt)
                                sed -i "s/alist-09ceb38a-f143-47f7-b255-c3eec819cd7b0lSmqjgBRIMJakAkbJIE2KzO6h2CUVBuEkqrLiA5cJJzOzYxJtCTIGBXXnhrg7Av/$alist_auth_token/" update.sql
                        fi

			pass=$(tr -dc '_A-Za-z0-9'  </dev/urandom  | head -c  32)	
                        sqlite3 /opt/alist/data/data.db <<EOF
drop table x_storages;
drop table x_meta;
drop table x_setting_items;
update x_users set password = "$pass" where id = 1;
update x_users set permission  = 368 where id = 2;
.read update.sql
EOF
			cp opentoken_url.txt /data/opentoken_url.txt
if ! cmp -s "/tmp/update.zip" "/var/lib/update.zip"; then
    mv -f "/var/lib/update.zip" "/var/lib/update.zip.bak"
    cp -f "/tmp/update.zip" "/var/lib/update.zip"
fi
                        rm update.zip update.sql opentoken_url.txt
			entries=$(echo "select count() from x_storages;" | sqlite3 /opt/alist/data/data.db)
			echo "$entries records have been updated into your database"
                fi
        fi				
		
fi	

cd /www
if [ -z "$download_url" ]; then
	echo "wget"$wget_para"update/tvbox.zip" | /bin/sh
else
	echo "wget "$download_url"/tvbox.zip"| /bin/sh
fi
if [ ! -f tvbox.zip ]; then
	cp /var/lib/tvbox.zip /www/tvbox.zip
fi
unzip -q -o tvbox.zip
if ! cmp -s "/www/tvbox.zip" "/var/lib/tvbox.zip"; then
    mv -f "/var/lib/tvbox.zip" "/var/lib/tvbox.zip.bak"
    cp -f "/www/tvbox.zip" "/var/lib/tvbox.zip"
fi
rm tvbox.zip

if [ -f /data/cat_passwd.txt ]; then
    cat_user=$(cat /data/cat_passwd.txt | cut -f1 -d":")
    cat_pass=$(cat /data/cat_passwd.txt | cut -f2 -d":")
    htpasswd -bc /www/tvbox/cat/.htpasswd $cat_user $cat_pass
fi

if [ -f /www/tvbox/cat/my_cat.json ]; then
    mytoken=$(cat /data/mytoken.txt | tr -d [:space:])
    sed -i "s/ALI_SHORT_TOKEN/$mytoken/" /www/tvbox/cat/my_cat.json
fi

if [ -f /data/my.json ]; then
        rm /www/tvbox/my.json
        ln -s /data/my.json /www/tvbox/my.json
fi
if [ -f /data/iptv.m3u ]; then
        ln -s /data/iptv.m3u /www/tvbox/iptv.m3u 2>/dev/null
fi

if [[ -f /data/docker_address.txt ]] && [[ -s /data/docker_address.txt ]]; then
	docker_address=$(head -n1 /data/docker_address.txt)
    if [ -s /data/docker_address_ext.txt ]; then
        docker_address_ext=$(head -n1 /data/docker_address_ext.txt)
        sed -i "s#DOCKER_ADDRESS#$docker_address_ext#g" /www/tvbox/my_ext.json
        sed -i "s#DOCKER_ADDRESS#$docker_address_ext#g" /www/tvbox/juhe_ext.json
        sed -i "s#DOCKER_ADDRESS#$docker_address_ext#g" /www/tvbox/json/alist_ext.json
    fi
    sed -i "s#DOCKER_ADDRESS#$docker_address#g" /www/tvbox/my.json
    sed -i "s#DOCKER_ADDRESS#$docker_address#g" /www/tvbox/json/alist.json
    sed -i "s#DOCKER_ADDRESS#$docker_address#g" /www/tvbox/json/myalist.json
    sed -i "s#DOCKER_ADDRESS#$docker_address#g" /www/tvbox/juhe.json
        if [ -f /data/tvbox_security.txt ]; then
                rm /www/tvbox/*.txt 2>/dev/null
                if [ ! -f /data/tvbox_config.txt ]; then
                        random_sub=$(tr -dc 'a-z0-9'  </dev/urandom  | head -c 8)
                        mv /www/tvbox/my.json /www/tvbox/$random_sub.my.txt 2>/dev/null
                        sed -i "s#/tvbox/my.json#/tvbox/$random_sub.my.txt#" /www/tvbox/juhe.json 2>/dev/null
                        mv /www/tvbox/juhe.json /www/tvbox/$random_sub.juhe.txt 2>/dev/null
                        echo $docker_address/tvbox/$random_sub.my.txt > /data/tvbox_config.txt
                        echo $docker_address/tvbox/$random_sub.juhe.txt >> /data/tvbox_config.txt
                fi

                if [ -f /data/tvbox_config.txt ]; then
                        myjson=$(head -n1 /data/tvbox_config.txt |cut -f5 -d"/")
                        mv /www/tvbox/my.json /www/tvbox/$myjson  2>/dev/null
                        head=$(echo $myjson|cut -f1 -d".")
                        sed -i "s#/tvbox/my.json#/tvbox/$myjson#" /www/tvbox/juhe.json 2>/dev/null
                        mv /www/tvbox/juhe.json /www/tvbox/$head.juhe.txt 2>/dev/null
                fi

                if [ ! -f /data/tvbox_config_ext.txt ]; then
                        random_sub=$(tr -dc 'a-z0-9'  </dev/urandom  | head -c 8)
                        mv /www/tvbox/my_ext.json /www/tvbox/$random_sub.my_ext.txt 2>/dev/null
                        sed -i "s#/tvbox/my_ext.json#/tvbox/$random_sub.my_ext.txt#" /www/tvbox/juhe_ext.json 2>/dev/null
                        mv /www/tvbox/juhe_ext.json /www/tvbox/$random_sub.juhe_ext.txt 2>/dev/null
                        echo $docker_address_ext/tvbox/$random_sub.my_ext.txt > /data/tvbox_config_ext.txt
                        echo $docker_address_ext/tvbox/$random_sub.juhe_ext.txt >> /data/tvbox_config_ext.txt
                fi

                if [ -f /data/tvbox_config_ext.txt ]; then
                        my_extjson=$(head -n1 /data/tvbox_config_ext.txt |cut -f5 -d"/")
                        mv /www/tvbox/my_ext.json /www/tvbox/$my_extjson  2>/dev/null
                        head=$(echo $my_extjson|cut -f1 -d".")
                        sed -i "s#/tvbox/my.json#/tvbox/$my_extjson#" /www/tvbox/juhe_ext.json 2>/dev/null
                        mv /www/tvbox/juhe_ext.json /www/tvbox/$head.juhe_ext.txt 2>/dev/null
                fi

        fi

    alist_auth=$(sqlite3 /opt/alist/data/data.db <<EOF
select value from x_setting_items where key = "token";
EOF
)
    sed -i "s#ALIST_AUTH#$alist_auth#" /www/tvbox/libs/alist.min.js
    sed -i "s#ALIST_AUTH#$alist_auth#" /www/tvbox/cat/libs/cat.alist.min.js
    sed -i "s#DOCKER_ADDRESS#$docker_address#g" /www/tvbox/libs/alist.min.js
fi
cd /tmp

if [[ -s /data/alist_token_expire_time.txt ]]; then
        expire_time=$(head -n1 /data/alist_token_expire_time.txt)
else
        expire_time=4800
fi
sed -i "s#ALIST_TOKEN_EXPIRE_TIME#$expire_time#" /opt/alist/data/config.json

if [ ! -f version.txt ]; then
	cp /var/lib/version.txt /tmp/version.txt
fi
if [ ! -f version.txt ]; then
        echo "Failed to download version.txt file, the index file upgrade process has aborted"
else	
	remote=$(head -n1 version.txt)
	echo "最新数据版本："$remote
        if [ ! -f /version.txt ]; then
                echo 0.0.0 > /version.txt
        fi
        local=$(head -n1 /version.txt)
        latest=$(printf "$remote\n$local\n" |sort -r |head -n1)
        if [ $remote = $local ]; then
                echo `date` "current index file version is updated, no need to upgrade"
        elif [ $remote = $latest ]; then
        if [ -z "$download_url" ]; then
            echo "wget"$wget_para"update/index.zip" |/bin/sh
        else
            echo "wget "$download_url"/index.zip" |/bin/sh
        fi
if [ ! -f index.zip ]; then
	cp /var/lib/index.zip /tmp/index.zip
fi
		if [ ! -f index.zip ]; then
			echo "Failed to download index compressed file, the index file upgrade process has aborted"
		else
			unzip -o -q -P abcd index.zip
			cat index.video.txt index.book.txt index.music.txt index.non.video.txt > /index/index.txt
			mv index*.txt /index/
			echo `date` "update index succesfully, your new version.txt is" $remote
			echo $remote > /version.txt
		fi
		
	else
		echo `date` "your current version.txt is updated, no need to downgrade"
		echo $remote > /version.txt
	fi
if ! cmp -s "/tmp/index.zip" "/var/lib/update.zip"; then
    mv -f "/var/lib/index.zip" "/var/lib/index.zip.bak"
    cp -f "/tmp/index.zip" "/var/lib/index.zip"
fi
	rm index.* 2>/dev/null
	rm update.* 2>/dev/null
if ! cmp -s "/tmp/version.txt" "/var/lib/version.txt"; then
    mv -f "/var/lib/version.txt" "/var/lib/version.txt.bak"
    cp -f "/tmp/version.txt" "/var/lib/version.txt"
fi
	rm version.txt
fi

sqlite3 /opt/alist/data/data.db <<EOF2
INSERT OR REPLACE INTO "x_storages" ("id", "mount_path", "order", "driver", "cache_expiration", "status", "addition", "remark", "modified", "disabled", "order_by", "order_direction", "extract_folder", "web_proxy", "webdav_policy", "down_proxy_url") VALUES (12306, '/ailg_jf', 0, 'AliyundriveShare2Open', 30, 'work', '{"RefreshToken":"3365fd36ce123065bd5d367ac77effef","RefreshTokenOpen":"eyJ0eXAiOiJKV1QiLCJhbabcdeJSUzI1NiJ9.eyJzdWIiOiI0OTIxYmE4ZjMxNTY0MTI3YWYyZWM0ZTM0MDg0MGNjYSIsImF1ZCI6Ijc2OTE3Y2NjY2Q0NDQxYzM5NDU3YTA0ZjYwODRmYjJmIiwiZXhwIjoxNzE5NTQyODYzLCJpYXabcdE3MTE3NjY4NjMsImp0aSI6IjgyZmYwZDcwNmQ2ODRkYTFhNmI0MWMwMThkYzZkYmM0In0.uEckc7w8lYL8IO0UPR5teU_Kz3f3FfmFm0UUBycKtc9Pz0mE31drnkTlHSSZ79lp8Um9SFFw74koGFSQquGbXQ","TempTransferFolderID":"655c7dc1dailgedad755494f9bc4ce5a754c0b75","share_id":"ULUbLKFH4sM","share_pwd":"8h5e","root_folder_id":"661d2f6559d2d8140c78407aa4020cd955b1352f","order_by":"name","order_direction":"ASC","oauth_token_url":"https://api.xhofe.top/alist/ali_open/token","client_id":"","client_secret":"","rorb":"r"}', '', '2023-03-23 12:28:29.866956394+00:00', 0, 'name', 'ASC', 'front', 0, '302_redirect', '');
EOF2
if [ -s /data/emby_server.txt ]; then
    emby_server=$(head -n1 /data/emby_server.txt)
    _docker_address=$(head -n1 /data/docker_address.txt)
sed -i "s#_embyHost#$emby_server#g" /etc/nginx/http.d/constant.js
sed -i "s#EMBY_SERVER#$_docker_address#g" /etc/nginx/http.d/emby.js
fi

if [ -s /data/jellyfin_server.txt ]; then
    jellyfin_server=$(head -n1 /data/jellyfin_server.txt)
sed -i "s#_jellyfinHost#$jellyfin_server#g" /etc/nginx/http.d/constant.js
fi

if [[ -s /data/emby_server.txt ]] && [[ ! -s /data/jellyfin_server.txt ]]; then
    jellyfin_server=$(head -n1 /data/emby_server.txt)
fi

if [ -s /data/infuse_api_key.txt ]; then
	infuse_api_key=$(head -n1 /data/infuse_api_key.txt)
sed -i "s#_embyApiKey#$infuse_api_key#g" /etc/nginx/http.d/constant.js
if [ -s /data/infuse_api_key_jf.txt ]; then
infuse_api_key_jf=$(head -n1 /data/infuse_api_key_jf.txt)
sed -i "s#_jellyfinApiKey#$infuse_api_key_jf#g" /etc/nginx/http.d/constant.js
fi
if [ -s /data/embyurl.txt ]; then
embyurl=$(head -n1 /data/embyurl.txt)
sed -i "s#_userEmbyUrl#$embyurl#g" /etc/nginx/http.d/externalUrl.js
else
sed -i "s#_userEmbyUrl##g" /etc/nginx/http.d/externalUrl.js
fi
if [ -s /data/jellyfinurl.txt ]; then
jellyfinurl=$(head -n1 /data/jellyfinurl.txt)
sed -i "s#_userJellyfinUrl#$jellyfinurl#g" /etc/nginx/http.d/externalUrl.js
else
sed -i "s#_userJellyfinUrl##g" /etc/nginx/http.d/externalUrl.js
fi
if [ -s /data/alisturl.txt ]; then
alisturl=$(head -n1 /data/alisturl.txt)
sed -i "s#_alistUrl#$alisturl#g" /etc/nginx/http.d/externalUrl.js
sed -i "s#_alistUrl#$alisturl#g" /etc/nginx/http.d/config/constant-mount.js
fi
mkdir -p /var/cache/nginx/emby/images
mkdir -p /var/cache/nginx/emby/subtitles
fi

exec "$@"

