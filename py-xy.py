import os
import time
import requests
import shutil
import socket
from datetime import datetime, timedelta
import subprocess
import pprint
from aligo import Aligo

# 配置部分
CHECK_INTERVAL = 24 * 3600  # 每天检查一次（秒）
FILES = ["all.mp4", "config.mp4", "115.mp4", "pikpak.mp4"]
BASE_URL = "http://{local_ip}:5678/d/元数据/"
DOWNLOAD_DIR = "/media"
EXTRACT_DIR = "/media/xiaoya"
LOG_FILE = "/media/process.log"
LOCAL_SIZE_FILE = "/media/local_size.txt"

def log(message):
    with open(LOG_FILE, "a") as log_file:
        log_file.write(f"{datetime.now()}: {message}\n")

def get_local_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # 连接到一个外部地址以获取本地IP地址
        s.connect(("*********", 80))  # 或者使用 ("***************", 80)
        local_ip = s.getsockname()[0]
    except Exception as e:
        print(f"Failed to get local IP: {e}")
        local_ip = "127.0.0.1"
    finally:
        s.close()
    return local_ip

def get_remote_file_size(file_url):
    for _ in range(5):
        try:
            response = requests.head(file_url, timeout=10, allow_redirects=True)
            if response.status_code == 200:
                remote_size = response.headers.get('Content-Length')
                if remote_size:
                    return int(remote_size)
        except requests.RequestException as e:
            print(f"Request failed: {e}")
        time.sleep(1)  # 增加延迟
    return None

def get_local_file_size(file_path, local_size_file):
    if os.path.exists(file_path):
        return os.path.getsize(file_path)

    # 如果文件不存在，从local_size.txt中获取文件大小
    if os.path.exists(local_size_file):
        with open(local_size_file, "r") as f:
            for line in f:
                parts = line.strip().split(" ")
                if len(parts) == 2 and parts[0] == os.path.basename(file_path):
                    return int(parts[1])

    return None

def download_and_extract(file_url, file_name):
    file_path = os.path.join(DOWNLOAD_DIR, file_name)
    
    # 检查 EXTRACT_DIR 是否存在且非空
    if os.path.exists(EXTRACT_DIR) and os.listdir(EXTRACT_DIR):
        backup_dir = EXTRACT_DIR + "-bak"
        if os.path.exists(backup_dir):
            shutil.rmtree(backup_dir)
        os.rename(EXTRACT_DIR, backup_dir)
    
    # 新建一个 EXTRACT_DIR 目录
    os.makedirs(EXTRACT_DIR, exist_ok=True)
    
    # 使用 aria2c 下载文件
    aria2c_command = [
        "aria2c", "-o", file_path, "--auto-file-renaming=false",
        "--allow-overwrite=true", "-c", "-x6", file_url
    ]
    print(f"正在下载 {file_name}，请耐心等待...")
    start_time = time.time()
    with subprocess.Popen(aria2c_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True) as proc:
        for line in proc.stdout:
            print(line, end='')  # 实时输出下载进度
        proc.wait()
        end_time = time.time()
        elapsed_time = end_time - start_time
        if proc.returncode == 0:
            print(f"{file_name} 下载完成，耗时 {int(elapsed_time // 3600)} 小时 {int((elapsed_time % 3600) // 60)} 分钟 {elapsed_time % 60:.2f} 秒")
            extract_command = ["7z", "x", "-aoa", "-bb0", "-mmt=16", file_path, f"-o{EXTRACT_DIR}"]
            print(f"正在解压 {file_name}，请耐心等待...")
            start_time = time.time()
            with subprocess.Popen(extract_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True) as proc:
                for line in proc.stdout:
                    print(line, end='')  # 实时输出解压进度
                proc.wait()
                end_time = time.time()
                elapsed_time = end_time - start_time
                if proc.returncode == 0:
                    #os.remove(file_path)
                    log(f"Downloaded and extracted {file_name}")
                    print(f"{file_name} 解压完成，耗时 {int(elapsed_time // 3600)} 小时 {int((elapsed_time % 3600) // 60)} 分钟 {elapsed_time % 60:.2f} 秒")
                else:
                    log(f"Failed to extract {file_name}: {proc.stderr.read()}")
        else:
            log(f"Failed to download {file_name}: {proc.stderr.read()}")

def process_files():
    if not os.path.exists(DOWNLOAD_DIR):
        log("Download directory does not exist. Exiting.")
        print("Download directory does not exist. Exiting.")
        return

    for file_name in FILES:
        file_path = os.path.join(DOWNLOAD_DIR, file_name)
        backup_path = file_path + ".bak"
        if os.path.exists(file_path):
            if os.path.exists(backup_path):
                os.remove(backup_path)
            os.rename(file_path, backup_path)

        file_url = BASE_URL.format(local_ip=get_local_ip()) + file_name
        remote_size = get_remote_file_size(file_url)
        local_size = get_local_file_size(file_path, LOCAL_SIZE_FILE)
        if remote_size is not None and local_size is not None and remote_size > 2000000000:
            if remote_size != local_size:
                download_and_extract(file_url, file_name)

def main():
    while True:
        now = datetime.now()
        next_run = (now + timedelta(days=1)).replace(hour=3, minute=0, second=0, microsecond=0)
        sleep_time = (next_run - now).total_seconds()
        time.sleep(sleep_time)
        process_files()

if __name__ == "__main__":
    main()
