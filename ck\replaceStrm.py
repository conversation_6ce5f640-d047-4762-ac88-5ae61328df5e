import os


def replace_content_in_strm_files(directory, old_path, new_path):
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.strm'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                # 替换文件内容
                new_lines = [
                    line.replace(old_path, new_path) if line.startswith(old_path) else line
                    for line in lines
                ]

                # 写入修改后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                print(f"已处理文件: {file_path}")


# 使用方法
if __name__ == "__main__":
    # 指定目标目录
    target_directory = "/share/CACHEDEV3_DATA/Strm/as_strm/cms01/国产剧"
    # 指定要替换的路径
    old_path = "/CloudNAS/cms01"
    new_path = "/home/<USER>/watch/Media/cloud_media/115cms01"
    replace_content_in_strm_files(target_directory, old_path, new_path)