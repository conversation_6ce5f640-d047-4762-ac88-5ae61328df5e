#!/bin/bash
# 创建目录结构
mkdir -p .github/workflows-origin
mkdir -p .github/workflows-new-origin
mkdir -p .github/workflows-origin333

# 将当前工作流文件复制到对应的目录
cp -r .github/workflows/* .github/workflows-origin/
cp -r .github/workflows/* .github/workflows-new-origin/
cp -r .github/workflows/* .github/workflows-origin333/

# 创建脚本文件
mkdir -p .git/hooks
touch .git/hooks/manage-workflows
chmod +x .git/hooks/manage-workflows

cat > .git/hooks/manage-workflows <<'EOF'
#!/bin/bash

command="$1"
remote="$2"

case "$command" in
  "switch")
    if [ -z "$remote" ]; then
      echo "Error: Remote name is required for switch command"
      exit 1
    fi
    
    # 检查远程特定的工作流目录是否存在
    if [ ! -d ".github/workflows-$remote" ]; then
      echo "Error: Workflow directory for remote '$remote' does not exist"
      exit 1
    fi
    
    echo "Switching to $remote workflows..."
    rm -rf .github/workflows
    mkdir -p .github/workflows
    cp -r .github/workflows-$remote/* .github/workflows/ 2>/dev/null || true
    # 记录当前活动的远程名称
    echo "$remote" > .github/.active_remote
    echo "Switched to $remote workflows"
    ;;
    
  "backup")
    echo "Backing up current workflows..."
    mkdir -p .github/workflows-backup
    rm -rf .github/workflows-backup/*
    cp -r .github/workflows/* .github/workflows-backup/ 2>/dev/null || true
    echo "Workflows backed up"
    ;;
    
  "restore")
    if [ ! -d ".github/workflows-backup" ]; then
      echo "Error: No workflow backup found"
      exit 1
    fi
    
    echo "Restoring workflows from backup..."
    rm -rf .github/workflows
    mkdir -p .github/workflows
    cp -r .github/workflows-backup/* .github/workflows/ 2>/dev/null || true
    echo "Workflows restored"
    ;;
    
  "list")
    echo "Available workflow directories:"
    for dir in .github/workflows-*; do
      if [ -d "$dir" ] && [ "$dir" != ".github/workflows-backup" ]; then
        echo "  - ${dir#.github/workflows-}"
      fi
    done
    ;;
  "status")
	  if [ -f ".github/.active_remote" ]; then
	    active_remote=$(cat .github/.active_remote)
	    echo "Current active workflows: $active_remote"
	  else
	    echo "No active remote recorded"
	  fi
	  ;;
    
  *)
    echo "Usage: manage-workflows <command> [remote]"
    echo ""
    echo "Commands:"
    echo "  switch <remote>  - Switch to workflows for specified remote"
    echo "  backup           - Backup current workflows"
    echo "  restore          - Restore workflows from backup"
    echo "  list             - List available workflow directories"
    exit 1
    ;;
esac
EOF

touch .git/hooks/pre-push
chmod +x .git/hooks/pre-push

cat > .git/hooks/pre-push <<'EOF'
#!/bin/bash

# 获取目标远程名称和URL
remote="$1"
url="$2"

echo "Preparing to push to remote: $remote"

# 检查当前活动的远程
if [ -f ".github/.active_remote" ]; then
  active_remote=$(cat .github/.active_remote)
  echo "Current active remote: $active_remote"
else
  active_remote="unknown"
  echo "No active remote recorded"
fi

# 检查工作流文件是否有修改
workflow_changes=$(git diff --name-only --cached | grep "^\.github/workflows/")

# 如果有工作流文件的修改，同步到对应的远程特定目录
if [ -n "$workflow_changes" ] && [ "$active_remote" != "unknown" ]; then
  echo "Detected workflow changes, syncing to remote-specific directory..."

  # 确保远程特定目录存在
  mkdir -p ".github/workflows-$active_remote"

  # 同步修改的文件
  for file in $workflow_changes; do
    filename=$(basename "$file")
    echo "Syncing $file to .github/workflows-$active_remote/$filename"
    cp "$file" ".github/workflows-$active_remote/$filename"
    git add ".github/workflows-$active_remote/$filename"
  done

  echo "Workflow changes synced to .github/workflows-$active_remote/"
fi

# 如果当前活动的远程与目标远程不匹配，切换工作流文件
if [ "$active_remote" != "$remote" ]; then
  echo "Switching workflows from $active_remote to $remote..."

  # 检查是否在交互式环境中
  if [ -t 0 ]; then
    # 在交互式环境中，提供确认提示
    echo "WARNING: You are pushing to $remote but current active workflows are for $active_remote"
    echo "The hook will automatically switch workflows for you."
    read -p "Continue with workflow switch and push? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      echo "Push aborted"
      exit 1
    fi
  else
    # 在非交互式环境中，自动继续
    echo "Non-interactive environment detected, automatically continuing with workflow switch"
  fi

  # 根据远程名称切换工作流文件
  if [ "$remote" = "origin" ] || [ "$remote" = "new-origin" ] || [ "$remote" = "origin333" ]; then
    # 备份当前工作流文件（如果存在且尚未同步）
    if [ -d ".github/workflows" ] && [ -z "$workflow_changes" ] && [ "$active_remote" != "unknown" ]; then
      echo "Backing up current workflows to .github/workflows-$active_remote/..."
      mkdir -p ".github/workflows-$active_remote"
      cp -r .github/workflows/* ".github/workflows-$active_remote/" 2>/dev/null || true
      git add ".github/workflows-$active_remote/"
    fi

    # 切换到目标远程的工作流文件
    echo "Switching to $remote workflows..."
    rm -rf .github/workflows
    mkdir -p .github/workflows
    cp -r ".github/workflows-$remote/"* .github/workflows/ 2>/dev/null || true

    # 记录当前活动的远程
    echo "$remote" > .github/.active_remote
  else
    echo "Unknown remote: $remote, keeping current workflows"
  fi

  # 添加更改到暂存区
  git add .github/workflows/

  echo "Workflows switched to $remote"
else
  echo "Already using $remote workflows, no switch needed"
fi

# 不要实际提交，让 Git 继续正常的推送流程
echo "Workflows prepared for $remote"
EOF

touch .git/hooks/post-commit
chmod +x .git/hooks/post-commit

cat > .git/hooks/post-commit <<'EOF'
#!/bin/bash

# 确定当前活动的远程工作流目录
# 这里我们假设有一个文件记录了当前活动的远程名称
if [ -f ".github/.active_remote" ]; then
  active_remote=$(cat .github/.active_remote)
  
  # 检查是否有工作流文件的更改
  workflow_changes=$(git diff-tree --no-commit-id --name-only -r HEAD | grep "^\.github/workflows/")
  
  if [ -n "$workflow_changes" ] && [ -d ".github/workflows-$active_remote" ]; then
    echo "Syncing workflow changes to .github/workflows-$active_remote/"
    
    # 对于每个更改的工作流文件，将其同步到远程特定目录
    for file in $workflow_changes; do
      # 提取文件名
      filename=$(basename "$file")
      # 同步文件
      cp "$file" ".github/workflows-$active_remote/$filename"
      echo "Synced $file to .github/workflows-$active_remote/$filename"
    done
    
    echo "Workflow changes synced to remote-specific directory"
  fi
else
  echo "No active remote recorded, skipping workflow sync"
fi
EOF

echo ".github/.active_remote" >> .gitignore

# workflows目录有变化时用git pp推送到相应的远程仓库
git config --local alias.pp '!f() { git push "$@" && ([ -x .git/hooks/post-push ] && .git/hooks/post-push || true); }; f'

# 切换工作流
git config --local alias.sw '!.git/hooks/manage-workflows switch'

# 列出可用的工作流
git config --local alias.list '!.git/hooks/manage-workflows list'

# 备份当前工作流
git config --local alias.backup '!.git/hooks/manage-workflows backup'

# 恢复工作流
git config --local alias.restore '!.git/hooks/manage-workflows restore'

# 状态查看
git config --local alias.wfs '!.git/hooks/manage-workflows status'
